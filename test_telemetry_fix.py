#!/usr/bin/env python3
"""Test script to verify telemetry configuration fixes."""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set environment variables to disable telemetry before importing anything
os.environ["OTEL_SDK_DISABLED"] = "true"
os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = ""
os.environ["LANGFUSE_PUBLIC_KEY"] = ""
os.environ["LANGFUSE_SECRET_KEY"] = ""

def test_telemetry_configuration():
    """Test that telemetry configuration prevents export errors."""
    print("🔧 Testing telemetry configuration...")
    
    try:
        # Import and test telemetry module
        from app.core.telemetry import setup_telemetry, disable_telemetry_exports
        
        print("✅ Telemetry module imported successfully")
        
        # Test disabling telemetry exports
        disable_telemetry_exports()
        print("✅ Telemetry exports disabled successfully")
        
        # Test Langfuse setup with empty credentials
        langfuse_client = setup_telemetry()
        if langfuse_client is None:
            print("✅ Langfuse correctly disabled (no credentials)")
        else:
            print("⚠️  Langfuse initialized unexpectedly")
        
        return True
        
    except Exception as e:
        print(f"❌ Telemetry configuration test failed: {e}")
        return False


def test_application_import():
    """Test that the main application can be imported without telemetry errors."""
    print("\n🚀 Testing application import...")
    
    try:
        # Import main application
        from app.main import app
        
        print("✅ FastAPI application imported successfully")
        print(f"✅ Application title: {app.title}")
        
        return True
        
    except Exception as e:
        print(f"❌ Application import failed: {e}")
        return False


def main():
    """Run all tests."""
    print("🧪 Testing Telemetry Configuration Fixes\n")
    
    # Test telemetry configuration
    telemetry_ok = test_telemetry_configuration()
    
    # Test application import
    app_ok = test_application_import()
    
    # Summary
    print("\n📊 Test Results:")
    print(f"   Telemetry Configuration: {'✅ PASS' if telemetry_ok else '❌ FAIL'}")
    print(f"   Application Import: {'✅ PASS' if app_ok else '❌ FAIL'}")
    
    if telemetry_ok and app_ok:
        print("\n🎉 All tests passed! Telemetry errors should be resolved.")
        print("\n💡 Next steps:")
        print("   1. Start your application: uvicorn app.main:app --reload")
        print("   2. Check logs for absence of telemetry connection errors")
        print("   3. Configure Langfuse credentials if you want observability")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
