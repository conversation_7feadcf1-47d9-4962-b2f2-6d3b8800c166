# Project ID Flow Summary

This document explains how `project_id` and `project_schedule_id` flow through the entire system from frontend request to GraphQL query.

## ✅ Complete Implementation Status

**YES**, the `project_id` and `project_schedule_id` are now properly passed to the agents and used in GraphQL queries.

## 🔄 Data Flow

### 1. Frontend Request
```json
{
  "messages": [
    {
      "role": "user",
      "content": "give me all site diary from jan to feb"
    }
  ],
  "project_id": 49,
  "project_schedule_id": 86,
  "user_id": "string",
  "auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 2. API Schema Validation
```python
# app/schemas/chat.py
class ChatRequest(BaseModel):
    project_id: Optional[int] = Field(default=None, ge=1)  # Integer validation
    project_schedule_id: Optional[int] = Field(default=None, ge=1)  # Integer validation
    # ... other fields
```

### 3. Agent State Preparation
```python
# app/core/langgraph/graph.py - get_response method
entities = {}
if project_id:
    entities["project_id"] = project_id  # 49
if project_schedule_id:
    entities["project_schedule_id"] = project_schedule_id  # 86

icms_state = {
    "messages": last_10_messages,
    "entities": entities,  # {"project_id": 49, "project_schedule_id": 86}
    "user_jwt": auth_token,
    # ... other fields
}
```

### 4. Schedule Team Agent Extraction
```python
# app/core/langgraph/subgraphs/schedule_team.py
def planning_agent_node(state: IcmsState) -> IcmsState:
    entities = state.get("entities", {})
    project_id = entities.get("project_id")  # 49
    project_schedule_id = entities.get("project_schedule_id")  # 86
    
    if not project_id or not project_schedule_id:
        # Return error message
        return error_response
    
    # Call tool with both IDs
    tool_call = {
        "name": "get_project_schedule",
        "args": {
            "project_id": project_id,  # 49
            "project_schedule_id": project_schedule_id,  # 86
            "jwt": user_jwt
        }
    }
```

### 5. GraphQL Tool Execution
```python
# app/core/langgraph/tools/icms_graphql_tools.py
@tool
async def get_project_schedule(project_id: int, project_schedule_id: int, jwt: str = "") -> dict:
    query = """
    query GetProjectSchedule($projectId: Int!, $projectScheduleId: Int!) {
        projectSchedule(projectId: $projectId, id: $projectScheduleId) {
            id
            name
            # ... other fields
        }
    }
    """
    
    variables = {
        "projectId": project_id,  # 49
        "projectScheduleId": project_schedule_id  # 86
    }
    
    result = await execute_graphql_query(query, variables, jwt)
```

### 6. GraphQL Query Sent to Backend
```graphql
query GetProjectSchedule($projectId: Int!, $projectScheduleId: Int!) {
    projectSchedule(projectId: $projectId, id: $projectScheduleId) {
        id
        name
        description
        tasks { ... }
        milestones { ... }
    }
}

# Variables:
{
  "projectId": 49,
  "projectScheduleId": 86
}
```

## 🛠️ Updated Components

### 1. API Schema (`app/schemas/chat.py`)
- ✅ Added `project_id: Optional[int]` with validation `ge=1`
- ✅ Added `project_schedule_id: Optional[int]` with validation `ge=1`
- ✅ Updated `auth_token` max length for JWT tokens

### 2. Agent Graph (`app/core/langgraph/graph.py`)
- ✅ Updated `get_response()` method signature
- ✅ Updated `get_stream_response()` method signature
- ✅ Entities preparation includes both project IDs
- ✅ Semantic cache uses project IDs for context

### 3. Schedule Team (`app/core/langgraph/subgraphs/schedule_team.py`)
- ✅ All agents extract both `project_id` and `project_schedule_id`
- ✅ Error handling for missing IDs
- ✅ Tool calls include both parameters

### 4. Cross-Module Team (`app/core/langgraph/subgraphs/cross_module.py`)
- ✅ Updated to use both project IDs
- ✅ Conditional logic for missing IDs

### 5. Entity Extraction (`app/core/langgraph/nodes/intent_classifier.py`)
- ✅ Updated `EntityExtraction` model to use integer types
- ✅ Added `project_schedule_id` field

### 6. Schedule Tools (`app/core/langgraph/tools/icms_graphql_tools.py`)
- ✅ All tools require both `project_id` and `project_schedule_id` as integers
- ✅ GraphQL queries use correct variable names

## 🔍 Validation & Error Handling

### Request Validation
```python
# Valid request
ChatRequest(
    messages=[...],
    project_id=49,  # ✅ Valid integer
    project_schedule_id=86  # ✅ Valid integer
)

# Invalid request
ChatRequest(
    messages=[...],
    project_id=0,  # ❌ Fails validation (ge=1)
    project_schedule_id=-1  # ❌ Fails validation (ge=1)
)
```

### Agent Error Handling
```python
# If either ID is missing
if not project_id or not project_schedule_id:
    error_message = AIMessage(
        content="Error: Both project_id and project_schedule_id are required for schedule queries."
    )
    return {**state, "messages": state["messages"] + [error_message]}
```

## 🧪 Test Results

All tests pass successfully:

```
✅ API accepts project_id and project_schedule_id as integers
✅ Agent state properly includes both IDs in entities  
✅ Schedule tools require both project_id and project_schedule_id
✅ GraphQL queries will receive correct integer parameters
✅ Error handling works for missing IDs
```

## 📋 Usage Examples

### Complete Request Example
```bash
curl -X POST "http://localhost:8000/api/v1/chatbot/chat/session123" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [
      {
        "role": "user", 
        "content": "give me all site diary from jan to feb"
      }
    ],
    "project_id": 49,
    "project_schedule_id": 86,
    "user_id": "user123",
    "auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }'
```

### Schedule-Specific Queries
```json
{
  "messages": [{"role": "user", "content": "show me critical path"}],
  "project_id": 49,
  "project_schedule_id": 86
}

{
  "messages": [{"role": "user", "content": "analyze resource allocation"}],
  "project_id": 49, 
  "project_schedule_id": 86
}

{
  "messages": [{"role": "user", "content": "get schedule details"}],
  "project_id": 49,
  "project_schedule_id": 86
}
```

## 🚀 Ready for Production

The system is now fully configured to:

1. **Accept** your exact JSON structure with integer project IDs
2. **Validate** the request parameters properly
3. **Pass** both IDs through the entire agent system
4. **Execute** GraphQL queries with correct integer parameters
5. **Handle** error cases when IDs are missing
6. **Support** all schedule-related operations

Your frontend can now send requests exactly as specified in your example, and the backend will properly route them to the GraphQL API with the correct project and schedule IDs! 🎉
