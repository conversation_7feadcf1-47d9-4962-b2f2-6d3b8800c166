# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **ICMS Multi-Agent System** - an enterprise-grade Intelligent Construction Management System powered by a sophisticated multi-agent architecture. The system provides intelligent document management, schedule optimization, and cross-module analysis for construction projects with sub-second response times and 95% SLA compliance.

## Development Commands

### Environment Setup
- **Install dependencies**: `uv sync`
- **Environment setup**: Use `make set-env ENV=development|staging|production|test` to configure environment

### Running the Application
- **Development**: `make dev` (with auto-reload)
- **Staging**: `make staging`
- **Production**: `make prod`

### Code Quality
- **Lint**: `make lint` (uses ruff check)
- **Format**: `make format` (uses ruff format)

### Testing & Evaluation
- **Interactive evaluation**: `make eval ENV=development`
- **Quick evaluation**: `make eval-quick ENV=development`
- **Evaluation without report**: `make eval-no-report ENV=development`

### Docker Operations
- **Build**: `make docker-build-env ENV=development`
- **Run**: `make docker-run-env ENV=development`
- **Logs**: `make docker-logs ENV=development`
- **Stop**: `make docker-stop ENV=development`
- **Full stack**: `make docker-compose-up ENV=development`

## Architecture Overview

### Multi-Agent Hierarchy
The system uses a **hierarchical multi-agent architecture** with:

1. **Main Supervisor** (Intent Classification) - Routes queries to specialized teams
2. **Document Team Sub-Graph** - Document search, analysis, and metadata management
3. **Schedule Team Sub-Graph** - Planning, critical path analysis, and resource management  
4. **Cross-Module Coordinator Sub-Graph** - Impact analysis and dependency mapping

### Key Components

#### Core Application (`app/`)
- **main.py**: FastAPI application entry point with middleware setup
- **core/config.py**: Environment-specific configuration management
- **api/v1/**: REST API endpoints and routing
- **schemas/**: Pydantic models for API and graph state
- **models/**: Database models and relationships
- **services/**: Business logic and external service integrations

#### LangGraph Multi-Agent System (`app/core/langgraph/`)
- **graph.py**: Main LangGraph orchestration and agent coordination
- **nodes/**: Individual agent nodes (intent classifier, ACL filter, routing, error handling)
- **subgraphs/**: Specialized team sub-graphs (document, schedule, cross-module)
- **tools/**: GraphQL tools and batch operations for ICMS backend
- **entities/**: Pydantic domain models (Project, Task, Document)
- **utils/**: Shared utilities (message management, semantic cache, dataloader)

### 12 Key Performance Refinements

1. **Message Optimization**: 80% prompt size reduction using last_10 slice + full_history_id
2. **Semantic Cache**: 30-50% instant responses with FAISS vector similarity
3. **Row-Level ACL**: JWT-based permissions with automatic GraphQL filtering
4. **Confidence Scoring**: 70% fewer silent mistakes with auto-clarification ≤0.7
5. **Pydantic Models**: Type-safe development with IDE support
6. **Multilingual Support**: Zero extra prompts for multi-language queries
7. **Batch Operations**: 3-5× faster comparisons with concurrent execution
8. **DataLoader Pattern**: N+1 query elimination for GraphQL operations
9. **Timeout Management**: 95% SLA compliance with 3-second guarantees
10. **Score-Based Router**: 90% routing accuracy with multi-factor scoring
11. **Shared Scratchpad**: Cross-module coordination via Redis namespaces
12. **Self-Healing Retry**: 80% error recovery with adaptive strategies

## Environment Configuration

The system supports multiple environments with specific .env files:
- `.env.development` - Local development settings
- `.env.staging` - Staging environment configuration  
- `.env.production` - Production deployment settings

### Required Environment Variables
```bash
# Azure OpenAI (primary LLM provider)
AZURE_OPENAI_ENDPOINT="your-azure-openai-endpoint"
AZURE_OPENAI_API_KEY="your-api-key"
AZURE_OPENAI_DEPLOYMENT_NAME="your-deployment"

# Redis (conversation memory, cache, scratchpad)
REDIS_URL="redis://localhost:6379/0"

# GraphQL Backend (ICMS Nest.js + MySQL)
GRAPHQL_ENDPOINT="http://localhost:3000/graphql"

# Langfuse (observability)
LANGFUSE_PUBLIC_KEY="your-public-key"
LANGFUSE_SECRET_KEY="your-secret-key"
```

## Development Guidelines

### Code Style
- Uses **ruff** for linting and formatting (line length: 119)
- Follows **Google docstring convention**
- Type hints required for all functions
- **No comments unless necessary** - code should be self-documenting

### Dependencies Management
- Uses **uv** for fast dependency management
- Core dependencies: FastAPI, LangGraph, LangChain, Redis, SQLModel
- Dev dependencies: ruff, black, isort, pytest

### Multi-Agent Development Patterns

#### Adding New Agent Nodes
1. Create node function in `app/core/langgraph/nodes/`
2. Import and add to graph in `graph.py:create_graph()`
3. Define routing logic in appropriate router function
4. Add corresponding state fields to `IcmsState` schema

#### Creating Sub-Graphs
1. Define sub-graph in `app/core/langgraph/subgraphs/`
2. Use specialized state models inheriting from base state
3. Include team supervisor + specialized tool nodes
4. Handle cross-team coordination via shared scratchpad

#### GraphQL Tool Integration
1. Add tools to `app/core/langgraph/tools/icms_graphql_tools.py`
2. Include ACL filtering and timeout management
3. Use DataLoader pattern for batch operations
4. Follow timeout SLA requirements (3 seconds max)

### Testing & Evaluation

The system includes a comprehensive evaluation framework:
- **Metrics**: Conciseness, hallucination, helpfulness, relevancy, toxicity
- **Reports**: Automated JSON reports with success rates and performance data
- **Traces**: Integration with Langfuse for observability

### Performance Monitoring

Key metrics to monitor:
- **Response times**: Target <3 seconds (95% SLA)
- **Cache hit rates**: Target 30-50%
- **Intent classification accuracy**: Target >90%
- **Error recovery rates**: Target >80%

## Troubleshooting

### Common Issues
1. **Redis connection failures**: Check REDIS_URL and Redis server status
2. **GraphQL timeouts**: Verify GRAPHQL_ENDPOINT and backend availability
3. **Azure OpenAI errors**: Validate API keys and deployment names
4. **ACL permission errors**: Check JWT token format and user permissions

### Development Tips
- Use `make dev` for local development with auto-reload
- Monitor logs for multi-agent coordination issues
- Check Redis for conversation state and cache data
- Use Langfuse for detailed trace analysis
- Test with different user roles for ACL validation