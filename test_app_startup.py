#!/usr/bin/env python3
"""Test script to verify the application starts without telemetry errors."""

import os
import sys
import time
import subprocess
import signal
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_app_startup():
    """Test that the application starts without telemetry connection errors."""
    print("🧪 Testing application startup without telemetry errors...")
    
    # Set environment variables to disable telemetry
    env = os.environ.copy()
    env.update({
        "OTEL_SDK_DISABLED": "true",
        "OTEL_EXPORTER_OTLP_ENDPOINT": "",
        "LANGFUSE_PUBLIC_KEY": "",
        "LANGFUSE_SECRET_KEY": "",
        "LOG_LEVEL": "INFO",  # Reduce log noise
    })
    
    # Start the application
    process = None
    try:
        print("🚀 Starting application...")
        process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "127.0.0.1",
            "--port", "8001",  # Use different port to avoid conflicts
            "--log-level", "info"
        ], env=env, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for startup
        startup_timeout = 30
        startup_time = 0
        
        while startup_time < startup_timeout:
            if process.poll() is not None:
                # Process has terminated
                stdout, stderr = process.communicate()
                print(f"❌ Application terminated unexpectedly")
                print(f"STDOUT:\n{stdout}")
                print(f"STDERR:\n{stderr}")
                return False
            
            time.sleep(1)
            startup_time += 1
            
            # Check if we can connect to the health endpoint
            try:
                import requests
                response = requests.get("http://127.0.0.1:8001/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Application started successfully!")
                    print("✅ Health endpoint responding")
                    
                    # Check the logs for telemetry errors
                    stdout, _ = process.communicate(timeout=2)
                    if "Connection refused" in stdout or "Failed to export batch" in stdout:
                        print("❌ Telemetry connection errors detected in logs")
                        return False
                    else:
                        print("✅ No telemetry connection errors detected")
                        return True
                        
            except (requests.exceptions.RequestException, subprocess.TimeoutExpired):
                # Still starting up
                continue
        
        print("❌ Application startup timeout")
        return False
        
    except Exception as e:
        print(f"❌ Error during startup test: {e}")
        return False
        
    finally:
        # Clean up
        if process and process.poll() is None:
            print("🧹 Cleaning up...")
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()

def main():
    """Run the startup test."""
    print("🧪 Application Startup Test\n")
    
    success = test_app_startup()
    
    if success:
        print("\n🎉 Test passed! Application starts without telemetry errors.")
        print("\n💡 You can now start your application normally:")
        print("   uvicorn app.main:app --reload")
        return 0
    else:
        print("\n❌ Test failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
