#!/usr/bin/env python3
"""Test script to verify the updated schedule tools work correctly."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_schedule_tools():
    """Test the updated schedule tools with integer IDs."""
    print("🧪 Testing updated schedule tools...")
    
    try:
        # Import the updated tools
        from app.core.langgraph.tools.icms_graphql_tools import (
            get_project_schedules,
            get_project_schedule,
            get_critical_path,
            analyze_resource_allocation
        )
        
        print("✅ Successfully imported updated schedule tools")
        
        # Test function signatures
        print("\n📋 Testing function signatures...")
        
        # Test get_project_schedules
        print("  - get_project_schedules: expects project_id (int)")
        
        # Test get_project_schedule  
        print("  - get_project_schedule: expects project_id (int), project_schedule_id (int)")
        
        # Test get_critical_path
        print("  - get_critical_path: expects project_id (int), project_schedule_id (int)")
        
        # Test analyze_resource_allocation
        print("  - analyze_resource_allocation: expects project_id (int), project_schedule_id (int)")
        
        print("\n✅ All function signatures updated correctly")
        
        # Test batch tools
        from app.core.langgraph.tools.batch_tools import batch_get_schedules
        print("  - batch_get_schedules: expects project_ids (List[int])")
        
        print("\n✅ Batch tools updated correctly")
        
        # Test tool registration
        from app.core.langgraph.tools import tools
        tool_names = [tool.name for tool in tools]
        
        expected_tools = [
            'get_project_schedules',
            'get_project_schedule', 
            'get_critical_path',
            'analyze_resource_allocation'
        ]
        
        print("\n📦 Checking tool registration...")
        for tool_name in expected_tools:
            if tool_name in tool_names:
                print(f"  ✅ {tool_name} registered")
            else:
                print(f"  ❌ {tool_name} NOT registered")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing schedule tools: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the schedule tools test."""
    print("🧪 Schedule Tools Update Test\n")
    
    success = asyncio.run(test_schedule_tools())
    
    if success:
        print("\n🎉 All tests passed! Schedule tools updated successfully.")
        print("\n📝 Summary of changes:")
        print("  • get_project_schedules(project_id: int) - List all schedules for a project")
        print("  • get_project_schedule(project_id: int, project_schedule_id: int) - Get specific schedule")
        print("  • get_critical_path(project_id: int, project_schedule_id: int) - Critical path analysis")
        print("  • analyze_resource_allocation(project_id: int, project_schedule_id: int) - Resource analysis")
        print("  • batch_get_schedules(project_ids: List[int]) - Batch schedule retrieval")
        print("\n💡 All functions now use integer IDs as required!")
        print("\n🎯 Frontend Integration:")
        print("  • Frontend provides both project_id and project_schedule_id as integers")
        print("  • Use get_project_schedules() to populate schedule selection dropdowns")
        print("  • Pass selected project_schedule_id to specific schedule functions")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
