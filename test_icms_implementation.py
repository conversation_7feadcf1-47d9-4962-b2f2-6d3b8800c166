#!/usr/bin/env python3
"""
Test script for ICMS Multi-Agent System implementation.

This script validates the core functionality of the new ICMS multi-agent system
without requiring external dependencies to be installed.
"""

import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_schema_imports():
    """Test that the new schemas can be imported."""
    try:
        from schemas.graph import IcmsState, GraphState
        print("✅ Schema imports successful")
        
        # Test IcmsState structure
        sample_state = {
            "messages": [],
            "intent": None,
            "entities": {},
            "gql_response": None,
            "requires_clarify": False,
            "user_jwt": ""
        }
        print("✅ IcmsState structure validated")
        return True
    except Exception as e:
        print(f"❌ Schema import failed: {e}")
        return False

def test_intent_classifier():
    """Test intent classifier logic."""
    try:
        # Test the fallback classification function
        from core.langgraph.nodes.intent_classifier import _fallback_classification
        
        sample_state = {
            "messages": [],
            "intent": None,
            "entities": {},
            "gql_response": None,
            "requires_clarify": False,
            "user_jwt": ""
        }
        
        # Test document query
        result = _fallback_classification(sample_state, "show me structural drawings")
        assert result["intent"] == "DOC_INTENT"
        print("✅ Document intent classification works")
        
        # Test schedule query
        result = _fallback_classification(sample_state, "show me overdue tasks")
        assert result["intent"] == "SCHED_INTENT"
        print("✅ Schedule intent classification works")
        
        # Test cross-module query
        result = _fallback_classification(sample_state, "what documents are blocking tasks")
        assert result["intent"] == "CROSS_INTENT"
        print("✅ Cross-module intent classification works")
        
        return True
    except Exception as e:
        print(f"❌ Intent classifier test failed: {e}")
        return False

def test_error_handling():
    """Test error handling nodes."""
    try:
        from core.langgraph.nodes.error_handling import clarification_node, fallback_node
        
        sample_state = {
            "messages": [],
            "intent": "DOC_INTENT",
            "entities": {},
            "gql_response": None,
            "requires_clarify": True,
            "user_jwt": ""
        }
        
        # Test clarification node
        result = clarification_node(sample_state)
        assert len(result["messages"]) > 0
        print("✅ Clarification node works")
        
        # Test fallback node
        result = fallback_node(sample_state)
        assert len(result["messages"]) > 0
        print("✅ Fallback node works")
        
        return True
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

def test_graphql_tools_structure():
    """Test GraphQL tools structure."""
    try:
        from core.langgraph.tools.icms_graphql_tools import (
            search_documents,
            get_project_schedule,
            get_critical_path,
            analyze_document_relationships,
            analyze_resource_allocation
        )
        
        # Check that tools have the required attributes
        assert hasattr(search_documents, 'name')
        assert hasattr(search_documents, 'description')
        print("✅ GraphQL tools structure validated")
        
        return True
    except Exception as e:
        print(f"❌ GraphQL tools test failed: {e}")
        return False

def test_subgraph_creation():
    """Test subgraph creation functions."""
    try:
        from core.langgraph.subgraphs import (
            create_document_team_subgraph,
            create_schedule_team_subgraph,
            create_cross_module_subgraph
        )
        
        print("✅ Subgraph creation functions imported successfully")
        return True
    except Exception as e:
        print(f"❌ Subgraph creation test failed: {e}")
        return False

def test_configuration():
    """Test configuration updates."""
    try:
        from core.config import settings
        
        # Check that new configuration options exist
        assert hasattr(settings, 'REDIS_URL')
        assert hasattr(settings, 'GRAPHQL_ENDPOINT')
        print("✅ Configuration updates validated")
        
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Testing ICMS Multi-Agent System Implementation")
    print("=" * 50)
    
    tests = [
        ("Schema Imports", test_schema_imports),
        ("Intent Classifier", test_intent_classifier),
        ("Error Handling", test_error_handling),
        ("GraphQL Tools", test_graphql_tools_structure),
        ("Subgraph Creation", test_subgraph_creation),
        ("Configuration", test_configuration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing {test_name}...")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} failed")
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! ICMS Multi-Agent System implementation is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
