#!/usr/bin/env python3
"""Test script to verify the schedule fallback mechanism works correctly."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_schedule_fallback():
    """Test the schedule fallback utilities."""
    print("🧪 Testing schedule fallback mechanism...")
    
    try:
        # Import the fallback utilities
        from app.core.langgraph.utils.schedule_fallback import (
            resolve_project_schedule_id,
            _select_best_schedule_with_query_context
        )
        
        print("✅ Successfully imported schedule fallback utilities")
        
        # Test schedule selection logic with mock data
        print("\n📋 Testing schedule selection logic...")
        
        # Mock schedules data
        mock_schedules = [
            {
                "id": 1,
                "name": "Project Baseline Schedule",
                "isActive": False,
                "status": "approved",
                "startDate": "2025-01-01T00:00:00Z",
                "endDate": "2025-12-31T23:59:59Z",
                "createdAt": "2024-12-01T00:00:00Z"
            },
            {
                "id": 2,
                "name": "Current Working Schedule v2",
                "isActive": True,
                "status": "active",
                "startDate": "2025-01-15T00:00:00Z",
                "endDate": "2025-11-30T23:59:59Z",
                "createdAt": "2025-01-10T00:00:00Z"
            },
            {
                "id": 3,
                "name": "Future Phase Schedule",
                "isActive": False,
                "status": "draft",
                "startDate": "2025-06-01T00:00:00Z",
                "endDate": "2026-05-31T23:59:59Z",
                "createdAt": "2025-01-05T00:00:00Z"
            }
        ]
        
        # Test different query contexts
        test_cases = [
            ("show me the current schedule", "Should select active schedule"),
            ("get baseline schedule", "Should select baseline schedule"),
            ("what's the latest schedule", "Should select most recent"),
            ("show project schedule", "Should select best overall")
        ]
        
        for query, expected in test_cases:
            selected = _select_best_schedule_with_query_context(mock_schedules, query)
            print(f"  Query: '{query}'")
            print(f"    Selected: {selected['name']} (ID: {selected['id']})")
            print(f"    Reason: {selected.get('_selection_reason')}")
            print(f"    Expected: {expected}")
            print()
        
        print("✅ Schedule selection logic working correctly")
        
        # Test the schedule team integration
        print("\n🔧 Testing schedule team integration...")
        
        from app.core.langgraph.subgraphs.schedule_team import (
            schedule_fallback_node,
            schedule_routing_condition,
            fallback_routing_condition
        )
        
        print("✅ Schedule team fallback nodes imported successfully")
        
        # Test routing conditions
        print("\n🔀 Testing routing conditions...")
        
        # Test case 1: No project_schedule_id should route to fallback
        state_no_schedule_id = {
            "entities": {"project_id": 123},
            "schedule_tools_to_use": ["get_project_schedule"]
        }
        
        route = schedule_routing_condition(state_no_schedule_id)
        print(f"  No schedule ID -> Route: {route} (expected: fallback)")
        
        # Test case 2: With project_schedule_id should route directly
        state_with_schedule_id = {
            "entities": {"project_id": 123, "project_schedule_id": 456},
            "schedule_tools_to_use": ["get_project_schedule"]
        }
        
        route = schedule_routing_condition(state_with_schedule_id)
        print(f"  With schedule ID -> Route: {route} (expected: planning)")
        
        print("\n✅ Routing conditions working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing schedule fallback: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the schedule fallback test."""
    print("🧪 Schedule Fallback Mechanism Test\n")
    
    success = asyncio.run(test_schedule_fallback())
    
    if success:
        print("\n🎉 All tests passed! Schedule fallback mechanism working correctly.")
        print("\n📝 How it works:")
        print("  1. User asks for schedule without specifying project_schedule_id")
        print("  2. Schedule supervisor routes to fallback node")
        print("  3. Fallback node intelligently selects best schedule based on:")
        print("     • Query context (keywords like 'current', 'baseline', etc.)")
        print("     • Schedule properties (active status, dates, etc.)")
        print("     • Name matching and recency")
        print("  4. Fallback node updates entities with selected project_schedule_id")
        print("  5. System routes to appropriate specialist agent")
        print("\n💡 Benefits:")
        print("  • Users don't need to know specific schedule IDs")
        print("  • Intelligent selection based on context")
        print("  • Fallback information provided for transparency")
        print("  • Graceful error handling when no schedules available")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
