# Agent-Level Schedule Fallback Implementation

This document describes the intelligent fallback mechanism implemented at the agent level for automatically selecting project schedules when users don't specify a `project_schedule_id`.

## Architecture Overview

Instead of implementing fallback logic in individual tools, we've implemented it at the **agent level** in the schedule team sub-graph. This provides:

- **Better separation of concerns**: Tools remain simple and focused
- **Centralized intelligence**: All fallback logic in one place
- **Context awareness**: Access to full conversation context and user query
- **Transparent operation**: Users get clear feedback about automatic selections

## How It Works

### 1. Flow Diagram

```
User Query: "show me the current schedule for project 123"
    ↓
Schedule Supervisor Node
    ↓ (no project_schedule_id in entities)
Schedule Fallback Node
    ↓ (resolves project_schedule_id = 456)
Planning Agent Node
    ↓
get_project_schedule(project_id=123, project_schedule_id=456)
```

### 2. Routing Logic

The schedule supervisor uses intelligent routing:

```python
def schedule_routing_condition(state):
    entities = state.get("entities", {})
    
    # Route to fallback if project_id exists but no project_schedule_id
    if entities.get("project_id") and not entities.get("project_schedule_id"):
        return "fallback"
    
    # Normal routing for other cases
    if "get_project_schedule" in tools_to_use:
        return "planning"
    # ... other routing logic
```

### 3. Intelligent Selection Algorithm

The fallback mechanism uses a sophisticated scoring system:

#### Query Context Scoring (Highest Priority: 60-80 points)
- **Direct name matching**: If query words match schedule names
- **Temporal keywords**: "current", "baseline", "latest", "active", "future"
- **Version matching**: "v1", "version 2", "rev 3", etc.

#### Schedule Properties Scoring
- **Active status**: 100 points for `isActive: true`
- **Current period**: 50 points if current date is within schedule dates
- **Priority names**: 30 points for names containing "baseline", "current", "main", etc.
- **Approved status**: 15 points for "approved", "published", "active" status
- **Recency**: Up to 20 points for recently created schedules

#### Example Scoring

For query "show me the current schedule":

```
Schedule A: "Project Baseline Schedule" (inactive) = 188 points
Schedule B: "Current Working Schedule v2" (active) = 349 points ✓
Schedule C: "Future Phase Schedule" (draft) = 144 points
```

Schedule B wins due to:
- Name match ("current") = 80 points
- Temporal match = 60 points  
- Active status = 100 points
- Current period = 50 points
- Priority name = 30 points
- Approved status = 15 points
- Recent creation = 14 points

## Implementation Details

### 1. Schedule Fallback Utility

**File**: `app/core/langgraph/utils/schedule_fallback.py`

**Key Functions**:
- `resolve_project_schedule_id()`: Main entry point for fallback resolution
- `_select_best_schedule_with_query_context()`: Intelligent selection algorithm
- `_score_schedule_by_query()`: Query-specific scoring logic

### 2. Schedule Team Integration

**File**: `app/core/langgraph/subgraphs/schedule_team.py`

**New Nodes**:
- `schedule_fallback_node`: Handles automatic schedule selection
- Updated routing conditions to include fallback logic

**Graph Flow**:
```
supervisor → fallback → [planning|critical_path|resource] → END
```

### 3. State Management

The fallback node updates the state with:

```python
{
    "entities": {
        "project_id": 123,
        "project_schedule_id": 456  # ← Added by fallback
    },
    "schedule_fallback_info": {
        "fallback_used": True,
        "selection_reason": "active, current_period, name_match",
        "selected_schedule": {
            "id": 456,
            "name": "Current Working Schedule v2",
            "total_available": 3
        },
        "all_available_schedules": [...]
    }
}
```

## Usage Examples

### Example 1: Current Schedule Request

**User**: "Show me the current schedule for project 123"

**Process**:
1. Entities extracted: `{"project_id": 123}`
2. No `project_schedule_id` → Route to fallback
3. Fallback finds 3 schedules, selects "Current Working Schedule v2"
4. Updates entities: `{"project_id": 123, "project_schedule_id": 456}`
5. Routes to planning agent
6. Planning agent calls `get_project_schedule(123, 456)`

**Response includes**:
- Schedule data
- Fallback information: "Automatically selected 'Current Working Schedule v2' (active schedule)"

### Example 2: Baseline Schedule Request

**User**: "Get the baseline schedule for project 123"

**Process**:
1. Query context scoring favors schedules with "baseline" in name
2. Selects "Project Baseline Schedule" even if not active
3. Provides transparent feedback about selection

### Example 3: No Schedules Available

**User**: "Show schedule for project 999"

**Process**:
1. Fallback attempts to find schedules
2. No schedules found for project 999
3. Returns error with clear message
4. Sets `requires_clarify: true` for user guidance

## Benefits

### 1. User Experience
- **No need to know schedule IDs**: Users can ask naturally
- **Context-aware selection**: System understands intent
- **Transparent operation**: Clear feedback about automatic selections
- **Graceful degradation**: Helpful errors when schedules unavailable

### 2. Developer Experience
- **Clean tool interfaces**: Tools remain simple and focused
- **Centralized logic**: All fallback intelligence in one place
- **Easy to extend**: Add new selection criteria easily
- **Testable**: Fallback logic can be unit tested independently

### 3. System Architecture
- **Separation of concerns**: Agent logic vs. tool logic
- **Maintainable**: Changes to fallback don't affect tools
- **Scalable**: Pattern can be applied to other agent teams
- **Observable**: Rich logging and state tracking

## Extending to Other Agents

This pattern can be applied to other agent teams:

### Document Team Fallback
- Auto-select document versions
- Choose appropriate document types
- Handle document relationship queries

### Cross-Module Fallback
- Coordinate schedule and document selections
- Handle complex multi-entity queries
- Provide unified fallback experience

## Configuration

The fallback mechanism can be configured through:

### Selection Weights
```python
FALLBACK_WEIGHTS = {
    "query_context": 80,      # Query keyword matching
    "active_status": 100,     # Active schedule preference
    "current_period": 50,     # Date-based relevance
    "priority_names": 30,     # Name-based priority
    "approved_status": 15,    # Status preference
    "recency": 20            # Creation date preference
}
```

### Temporal Keywords
```python
TEMPORAL_KEYWORDS = {
    "current": ["current", "now", "today", "present"],
    "baseline": ["baseline", "original", "planned", "initial"],
    "latest": ["latest", "newest", "recent", "updated"],
    "active": ["active", "ongoing", "running"],
    "future": ["future", "upcoming", "next", "planned"]
}
```

## Testing

Run the test suite to verify fallback functionality:

```bash
python test_schedule_fallback.py
```

The test covers:
- Selection algorithm accuracy
- Query context understanding
- Routing condition logic
- Error handling scenarios
- State management correctness

## Monitoring

The fallback mechanism provides rich logging:

```
schedule_fallback_triggered: project_id=123, reason=no_schedule_id_provided
schedule_selection_scoring: selected_score=349, total_candidates=3
schedule_fallback_completed: selected_schedule_id=456, selection_reason=active,current_period
```

This enables monitoring of:
- Fallback usage frequency
- Selection accuracy
- User satisfaction with automatic selections
- Performance impact of fallback operations
