# Frontend Integration Guide - Project Schedule Support

This guide explains how to integrate the updated schedule tools with your frontend application.

## Overview

The backend now supports multiple project schedules per project. The frontend needs to:
1. Provide both `project_id` and `project_schedule_id` as integers
2. Use the schedule listing API to populate selection dropdowns
3. Pass the selected schedule ID to specific schedule functions

## API Integration

### 1. List Available Schedules

**Endpoint**: Use the chatbot API with schedule listing query

**Request**:
```json
{
  "message": "list all schedules for project 123",
  "session_id": "user_session_123"
}
```

**Response** (simplified):
```json
{
  "response": "Found 3 schedules for project 123",
  "data": {
    "schedules": [
      {
        "id": 456,
        "name": "Project Baseline Schedule",
        "isActive": false,
        "status": "approved",
        "startDate": "2025-01-01T00:00:00Z",
        "endDate": "2025-12-31T23:59:59Z"
      },
      {
        "id": 457,
        "name": "Current Working Schedule v2",
        "isActive": true,
        "status": "active",
        "startDate": "2025-01-15T00:00:00Z",
        "endDate": "2025-11-30T23:59:59Z"
      },
      {
        "id": 458,
        "name": "Future Phase Schedule",
        "isActive": false,
        "status": "draft",
        "startDate": "2025-06-01T00:00:00Z",
        "endDate": "2026-05-31T23:59:59Z"
      }
    ]
  }
}
```

### 2. Get Specific Schedule Details

**Request**:
```json
{
  "message": "get schedule details for project 123 schedule 457",
  "session_id": "user_session_123"
}
```

**Response** (simplified):
```json
{
  "response": "Here are the details for Current Working Schedule v2",
  "data": {
    "schedule": {
      "id": 457,
      "name": "Current Working Schedule v2",
      "tasks": [
        {
          "id": 1001,
          "name": "Foundation Work",
          "status": "in_progress",
          "progress": 75,
          "baselineStart": "2025-01-15T00:00:00Z",
          "baselineFinish": "2025-02-15T00:00:00Z"
        }
      ],
      "milestones": [
        {
          "id": 2001,
          "name": "Foundation Complete",
          "date": "2025-02-15T00:00:00Z",
          "status": "pending"
        }
      ]
    }
  }
}
```

## Frontend Implementation Examples

### React Component Example

```jsx
import React, { useState, useEffect } from 'react';

const ScheduleSelector = ({ projectId, onScheduleSelect }) => {
  const [schedules, setSchedules] = useState([]);
  const [selectedScheduleId, setSelectedScheduleId] = useState(null);
  const [loading, setLoading] = useState(false);

  // Load available schedules when project changes
  useEffect(() => {
    if (projectId) {
      loadSchedules(projectId);
    }
  }, [projectId]);

  const loadSchedules = async (projectId) => {
    setLoading(true);
    try {
      const response = await fetch('/api/v1/chatbot/chat/session123', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: `list all schedules for project ${projectId}`,
          session_id: 'session123'
        })
      });
      
      const data = await response.json();
      setSchedules(data.data?.schedules || []);
      
      // Auto-select active schedule if available
      const activeSchedule = data.data?.schedules?.find(s => s.isActive);
      if (activeSchedule) {
        setSelectedScheduleId(activeSchedule.id);
        onScheduleSelect(activeSchedule.id);
      }
    } catch (error) {
      console.error('Failed to load schedules:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleScheduleChange = (scheduleId) => {
    setSelectedScheduleId(parseInt(scheduleId));
    onScheduleSelect(parseInt(scheduleId));
  };

  return (
    <div className="schedule-selector">
      <label htmlFor="schedule-select">Select Schedule:</label>
      <select
        id="schedule-select"
        value={selectedScheduleId || ''}
        onChange={(e) => handleScheduleChange(e.target.value)}
        disabled={loading || schedules.length === 0}
      >
        <option value="">Select a schedule...</option>
        {schedules.map(schedule => (
          <option key={schedule.id} value={schedule.id}>
            {schedule.name} {schedule.isActive ? '(Active)' : ''}
          </option>
        ))}
      </select>
      
      {loading && <span>Loading schedules...</span>}
      {schedules.length === 0 && !loading && (
        <span>No schedules available for this project</span>
      )}
    </div>
  );
};

// Usage in parent component
const ProjectDashboard = () => {
  const [projectId] = useState(123);
  const [selectedScheduleId, setSelectedScheduleId] = useState(null);

  const handleScheduleQuery = async (query) => {
    if (!selectedScheduleId) {
      alert('Please select a schedule first');
      return;
    }

    const response = await fetch('/api/v1/chatbot/chat/session123', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: `${query} for project ${projectId} schedule ${selectedScheduleId}`,
        session_id: 'session123'
      })
    });

    const data = await response.json();
    // Handle response...
  };

  return (
    <div>
      <h1>Project {projectId} Dashboard</h1>
      
      <ScheduleSelector
        projectId={projectId}
        onScheduleSelect={setSelectedScheduleId}
      />
      
      <div className="schedule-actions">
        <button onClick={() => handleScheduleQuery('get schedule details')}>
          View Schedule Details
        </button>
        <button onClick={() => handleScheduleQuery('get critical path')}>
          View Critical Path
        </button>
        <button onClick={() => handleScheduleQuery('analyze resource allocation')}>
          Analyze Resources
        </button>
      </div>
    </div>
  );
};
```

### Vue.js Component Example

```vue
<template>
  <div class="schedule-selector">
    <label for="schedule-select">Select Schedule:</label>
    <select
      id="schedule-select"
      v-model="selectedScheduleId"
      @change="onScheduleChange"
      :disabled="loading || schedules.length === 0"
    >
      <option value="">Select a schedule...</option>
      <option
        v-for="schedule in schedules"
        :key="schedule.id"
        :value="schedule.id"
      >
        {{ schedule.name }} {{ schedule.isActive ? '(Active)' : '' }}
      </option>
    </select>
    
    <span v-if="loading">Loading schedules...</span>
    <span v-else-if="schedules.length === 0">
      No schedules available for this project
    </span>
  </div>
</template>

<script>
export default {
  name: 'ScheduleSelector',
  props: {
    projectId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      schedules: [],
      selectedScheduleId: null,
      loading: false
    };
  },
  watch: {
    projectId: {
      handler(newProjectId) {
        if (newProjectId) {
          this.loadSchedules(newProjectId);
        }
      },
      immediate: true
    }
  },
  methods: {
    async loadSchedules(projectId) {
      this.loading = true;
      try {
        const response = await fetch('/api/v1/chatbot/chat/session123', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            message: `list all schedules for project ${projectId}`,
            session_id: 'session123'
          })
        });
        
        const data = await response.json();
        this.schedules = data.data?.schedules || [];
        
        // Auto-select active schedule
        const activeSchedule = this.schedules.find(s => s.isActive);
        if (activeSchedule) {
          this.selectedScheduleId = activeSchedule.id;
          this.onScheduleChange();
        }
      } catch (error) {
        console.error('Failed to load schedules:', error);
      } finally {
        this.loading = false;
      }
    },
    
    onScheduleChange() {
      this.$emit('schedule-selected', this.selectedScheduleId);
    }
  }
};
</script>
```

## Query Patterns

### Natural Language Queries

The backend supports natural language queries. Include both project ID and schedule ID:

```javascript
// Good examples:
"get schedule details for project 123 schedule 457"
"show critical path for project 123 schedule 457"
"analyze resource allocation for project 123 schedule 457"

// Also supported:
"show me the schedule for project 123 schedule 457"
"what's the critical path for project 123 schedule 457"
"check resource conflicts for project 123 schedule 457"
```

### Structured Queries

For more predictable parsing, use structured formats:

```javascript
const queries = {
  listSchedules: `list all schedules for project ${projectId}`,
  getSchedule: `get schedule details for project ${projectId} schedule ${scheduleId}`,
  getCriticalPath: `get critical path for project ${projectId} schedule ${scheduleId}`,
  getResources: `analyze resource allocation for project ${projectId} schedule ${scheduleId}`
};
```

## Error Handling

Handle common error scenarios:

```javascript
const handleScheduleQuery = async (query) => {
  try {
    const response = await fetch('/api/v1/chatbot/chat/session123', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ message: query, session_id: 'session123' })
    });

    const data = await response.json();
    
    // Check for errors in response
    if (data.error || data.data?.error) {
      throw new Error(data.error || data.data.error);
    }
    
    return data;
    
  } catch (error) {
    // Handle specific error cases
    if (error.message.includes('No schedules found')) {
      alert('No schedules available for this project');
    } else if (error.message.includes('project_schedule_id')) {
      alert('Please select a valid schedule');
    } else {
      alert(`Error: ${error.message}`);
    }
    
    throw error;
  }
};
```

## Best Practices

1. **Always validate IDs**: Ensure project_id and project_schedule_id are integers
2. **Auto-select active schedules**: When loading schedules, auto-select the active one
3. **Provide clear feedback**: Show loading states and error messages
4. **Cache schedule lists**: Avoid re-fetching schedules unnecessarily
5. **Handle empty states**: Gracefully handle projects with no schedules
6. **Use structured queries**: For better parsing reliability

## Testing

Test your integration with these scenarios:

1. **Project with multiple schedules**: Verify dropdown populates correctly
2. **Project with one schedule**: Should auto-select the single schedule
3. **Project with no schedules**: Should show appropriate message
4. **Active vs inactive schedules**: Verify active schedules are highlighted
5. **Error scenarios**: Test with invalid project IDs
