# Telemetry Troubleshooting Guide

This guide helps resolve telemetry-related errors in the ICMS Multi-Agent System.

## Common Telemetry Errors

### 1. Langfuse Authentication Errors

**Error:**
```
Failed to export batch code: 401, reason: {"message":"Invalid credentials. Confirm that you've configured the correct host."}
```

**Solution:**
- Set empty Langfuse credentials in `.env` to disable:
  ```bash
  LANGFUSE_PUBLIC_KEY=""
  LANGFUSE_SECRET_KEY=""
  ```

### 2. OTLP Connection Refused Errors

**Error:**
```
ConnectionRefusedError: [<PERSON>rrno 61] Connection refused
HTTPConnectionPool(host='localhost', port=4318): Max retries exceeded
```

**Solution:**
- Disable OpenTelemetry exports in `.env`:
  ```bash
  OTEL_SDK_DISABLED=true
  OTEL_EXPORTER_OTLP_ENDPOINT=""
  ```

## Environment Configuration

### Development Setup (Recommended)

Create a `.env` file with telemetry disabled:

```bash
# Application Settings
APP_ENV=development
PROJECT_NAME="ICMS Multi-Agent System"
DEBUG=true

# Disable Telemetry for Development
LANGFUSE_PUBLIC_KEY=""
LANGFUSE_SECRET_KEY=""
OTEL_SDK_DISABLED=true
OTEL_EXPORTER_OTLP_ENDPOINT=""

# Your other configuration...
AZURE_OPENAI_API_KEY="your-key"
REDIS_URL="redis://localhost:6379/0"
```

### Production Setup with Observability

For production with full observability:

```bash
# Application Settings
APP_ENV=production

# Langfuse Configuration
LANGFUSE_PUBLIC_KEY="pk-lf-your-public-key"
LANGFUSE_SECRET_KEY="sk-lf-your-secret-key"
LANGFUSE_HOST="https://cloud.langfuse.com"

# OpenTelemetry Configuration
OTEL_SDK_DISABLED=false
OTEL_EXPORTER_OTLP_ENDPOINT="http://your-otlp-collector:4318"
```

## Testing the Fix

Run the test script to verify telemetry configuration:

```bash
python test_telemetry_fix.py
```

Expected output:
```
🎉 All tests passed! Telemetry errors should be resolved.
```

## Manual Verification

1. **Start the application:**
   ```bash
   uvicorn app.main:app --reload
   ```

2. **Check for absence of these errors:**
   - ❌ `Failed to export batch code: 401`
   - ❌ `Connection refused` to localhost:4318
   - ❌ `Invalid credentials` for Langfuse

3. **Look for these success messages:**
   - ✅ `telemetry_exports_disabled`
   - ✅ `langfuse_disabled`
   - ✅ `opentelemetry_disabled`

## Enabling Observability Later

When you're ready to enable observability:

1. **Get Langfuse credentials:**
   - Sign up at [cloud.langfuse.com](https://cloud.langfuse.com)
   - Create a project and get your public/secret keys

2. **Update environment variables:**
   ```bash
   LANGFUSE_PUBLIC_KEY="pk-lf-your-actual-key"
   LANGFUSE_SECRET_KEY="sk-lf-your-actual-key"
   ```

3. **Optional: Set up OTLP collector:**
   - Install and configure an OpenTelemetry collector
   - Set `OTEL_EXPORTER_OTLP_ENDPOINT` to your collector URL
   - Set `OTEL_SDK_DISABLED=false`

## Architecture Notes

The telemetry system is designed to:
- **Fail gracefully** when credentials are missing
- **Disable exports** automatically in development
- **Log configuration** for debugging
- **Prevent connection errors** from affecting application functionality

The telemetry configuration is handled in:
- `app/core/telemetry.py` - Main telemetry setup
- `app/main.py` - Application initialization
- `app/core/config.py` - Environment variable configuration
