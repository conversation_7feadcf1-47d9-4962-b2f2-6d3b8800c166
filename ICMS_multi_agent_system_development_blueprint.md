# ICMS Multi-Agent System – Development Blueprint

(Turning the PRD into an executable plan)

## 1. High-Level Roadmap (16-Week Sprint Grid)

| Sprint | Focus              | LangGraph Layer                    | Deliverable Demo                                           | Exit Criteria                         |
| ------ | ------------------ | ---------------------------------- | ---------------------------------------------------------- | ------------------------------------- |
| 0      | Bootstrap          | Graph + State schema               | “Hello Agent” on FastAPI                                   | Local dev loop <3 min                 |
| 1      | GraphQL Mesh       | Tool nodes for Docs & Schedule     | `/ask` endpoint returns mock data                          | 100 % type-safe GQL calls             |
| 2      | Intent Router      | Supervisor + conditional edges     | “Show me docs for Project Alpha” works                     | 90 % intent match on test set         |
| 3      | Document Team      | Sub-graph with 3 specialist agents | Search, metadata, relations live                           | <2 s p95 Doc query                    |
| 4      | Schedule Team      | Sub-graph with 3 specialist agents | Critical path & resource answers                           | <2 s p95 Schedule query               |
| 5      | Memory & Context   | Checkpointer + thread ID           | Multi-turn “…and for next week?”                           | Conversation history survives restart |
| 6      | Error & Clarify    | Fallback + human-in-the-loop edges | “I didn’t get that—did you mean…?”                         | 100 % graceful failures               |
| 7      | Cross-Module       | Coordinator agent + shared state   | Impact analysis: “If drawing X changes, which tasks slip?” | End-to-end <10 s                      |
| 8      | Security Hardening | Auth middleware inside state       | JWT-scoped queries only                                    | Pen-test pass                         |
| 9-12   | Scale & Polish     | Caching, rate-limit, observability | LangSmith traces + Grafana boards                          | 99.5 % uptime on staging              |
| 13-16  | UAT & Launch       | Blue-green deploy                  | Training docs + rollout comms                              | 70 % adoption target met              |

## 2. LangGraph-first Architecture Diagram

```mermaid
graph TD
    User[User via Next.js Chat] -->|HTTP/WS| API[FastAPI Gateway]
    API -->|thread_id| LangGraphRuntime
    LangGraphRuntime --> Supervisor[Supervisor Node<br>intent_classifier]
    Supervisor -->|DOC_INTENT| DocTeam[Document Sub-Graph]
    Supervisor -->|SCHED_INTENT| SchedTeam[Schedule Sub-Graph]
    Supervisor -->|CROSS_INTENT| CrossTeam[Cross-Module Sub-Graph]
    DocTeam --> GQLDocs[(GraphQL Docs)]
    SchedTeam --> GQLSched[(GraphQL Schedule)]
    CrossTeam --> GQLDocs & GQLSched
    LangGraphRuntime --> Cache[(Redis Checkpointer)]
    LangGraphRuntime --> Auth[JWT Auth Guard]
```

## 3. Data Model (State Schema)

```python
from typing import List, TypedDict, Annotated, Optional
from langgraph.graph import add_messages

class IcmsState(TypedDict):
    messages: Annotated[List[BaseMessage], add_messages]
    intent: Optional[str]
    entities: dict            # extracted project, date, etc.
    gql_response: Optional[dict]
    requires_clarify: bool
    user_jwt: str             # for row-level authz
```

## 4. Core Node Patterns

### 4.1 Intent Classifier (Supervisor)

```python
def classify_intent_node(state: IcmsState):
    llm = ChatOpenAI(model="gpt-4o-mini")
    prompt = hub.pull("icms/intent-classifier")
    result = llm.invoke(prompt.format_messages(query=state["messages"][-1]))
    return {"intent": result.intent, "entities": result.entities}
```

### 4.2 Document Search Tool (Re-using LangGraph ToolNode)

```python
@tool
async def search_documents(project_id: str, term: str, jwt: str) -> dict:
    async with httpx.AsyncClient(headers={"Authorization": f"Bearer {jwt}"}) as c:
        r = await c.post(
            GQL_URL,
            json={"query": SEARCH_DOCS_GQL, "variables": {"projectId": project_id, "searchTerm": term}}
        )
    return r.json()["data"]["documents"]
```

Register the tool in a `ToolNode` and wire into the Document sub-graph.

## 5. Conversation Memory & Human-in-the-Loop

**Persistence**: Use `RedisSaver` (checkpointer) with `thread_id` = ICMS user session ID.  
**Interruption**: If `requires_clarify=True`, transition to `human_feedback` node that sends a socket message to the Next.js client and waits for reply.

## 6. Performance Cheat-Sheet

- **GraphQL cost analysis**: Reject queries >10 k edges.
- **Sub-graph caching**: 5 min TTL on Document metadata.
- **Streaming**: Use LangGraph `.astream()` for incremental tokens to the UI.
