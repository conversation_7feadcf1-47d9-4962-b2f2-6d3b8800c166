# ICMS Multi-Agent System - Troubleshooting Playbook

## 🚨 Emergency Response Guide

This playbook provides step-by-step solutions for common operational issues. Use this as your first reference when problems occur.

---

## 🔥 Critical Issues (Immediate Action Required)

### **Issue 1: System Down / Health Check Failing**

#### **Symptoms**
- `curl http://localhost:8000/health` returns 5xx or times out
- No response from any endpoints
- Users cannot access the system

#### **Immediate Actions**
```bash
# 1. Check if the process is running
ps aux | grep uvicorn

# 2. Check system resources
top -p $(pgrep -f uvicorn)
df -h  # Check disk space
free -m  # Check memory

# 3. Check critical dependencies
redis-cli ping  # Should return PONG
curl -X POST $GRAPHQL_ENDPOINT -d '{"query": "{ __typename }"}'  # Should return data

# 4. Check recent logs for errors
tail -50 logs/app.log | grep -E "(ERROR|CRITICAL|Exception)"
```

#### **Resolution Steps**
1. **If Redis is down**: `brew services restart redis` (macOS) or `sudo systemctl restart redis` (Linux)
2. **If GraphQL backend is down**: Contact backend team or restart backend service
3. **If ICMS process crashed**: `make dev` to restart with debug logging
4. **If out of memory**: Restart system and check for memory leaks in scratchpad

#### **Prevention**
- Set up monitoring alerts for health endpoint
- Implement automatic restart on failure
- Monitor Redis memory usage

---

### **Issue 2: Extremely Slow Responses (>10 seconds)**

#### **Symptoms**
- All queries taking >10 seconds
- Timeout errors in logs
- Users complaining about performance

#### **Immediate Diagnosis**
```bash
# 1. Check cache performance
curl http://localhost:8000/metrics/cache
# Look for: hit_rate < 0.1 (very low)

# 2. Check for N+1 queries
grep "dataloader_batch" logs/app.log | tail -10
# Should see batching, not individual queries

# 3. Check Redis performance
redis-cli --latency-history -i 1
# Should be <1ms consistently

# 4. Check GraphQL backend response times
grep "graphql_query_duration" logs/app.log | tail -10
```

#### **Quick Fixes**
```bash
# 1. Clear semantic cache if corrupted
redis-cli FLUSHDB

# 2. Restart with cache warming
curl -X POST "http://localhost:8000/admin/warm-cache"

# 3. Check for memory pressure
free -m
# If low memory, restart system
```

#### **Root Cause Analysis**
- **Cache corruption**: Clear and rebuild cache
- **GraphQL backend slow**: Contact backend team
- **Memory pressure**: Scale up or optimize memory usage
- **Network issues**: Check connectivity to Redis/GraphQL

---

## ⚠️ High Priority Issues

### **Issue 3: Wrong Agent Selection / Poor Intent Classification**

#### **Symptoms**
- Document queries going to schedule team
- Schedule queries going to document team
- Users getting irrelevant responses

#### **Diagnosis**
```bash
# 1. Check recent intent classifications
grep "intent_classification_completed" logs/app.log | tail -20

# 2. Look for low confidence scores
grep "confidence_score" logs/app.log | awk -F'confidence_score:' '{print $2}' | awk '{print $1}' | sort -n

# 3. Check clarification triggers
grep "requires_clarify.*true" logs/app.log | tail -10
```

#### **Analysis Patterns**
```bash
# Check confidence score distribution
grep "confidence_score" logs/app.log | awk -F'confidence_score:' '{print $2}' | awk '{print $1}' | \
  awk '{
    if ($1 <= 0.5) low++; 
    else if ($1 <= 0.7) medium++; 
    else high++
  } 
  END {
    print "Low (≤0.5):", low; 
    print "Medium (0.5-0.7):", medium; 
    print "High (>0.7):", high
  }'
```

#### **Solutions**
1. **If confidence scores consistently low (<0.5)**:
   ```bash
   # Adjust confidence threshold temporarily
   export ICMS_CONFIDENCE_THRESHOLD=0.5
   make dev
   ```

2. **If entity extraction failing**:
   ```python
   # Test entity extraction manually
   from app.core.langgraph.nodes.intent_classifier import classify_intent_node
   result = await classify_intent_node({
       "messages": [HumanMessage(content="your problematic query")]
   })
   print(result["entities"])
   ```

3. **If multilingual issues**:
   - Check OpenAI function calling is working
   - Verify multilingual test cases

---

### **Issue 4: Permission/Security Errors**

#### **Symptoms**
- Users getting 403 Forbidden errors
- "Access denied" messages
- Users seeing data they shouldn't

#### **Diagnosis**
```bash
# 1. Check JWT token format
echo "your-jwt-token" | base64 -d | jq .

# 2. Check ACL filter logs
grep "acl_filter_applied" logs/app.log | tail -10

# 3. Check permission extraction
grep "user_permissions" logs/app.log | tail -5

# 4. Check GraphQL filter application
grep "graphql_acl_filter" logs/app.log | tail -10
```

#### **Common JWT Issues**
```python
# Validate JWT structure
import jwt
token = "your-jwt-token"
try:
    decoded = jwt.decode(token, options={"verify_signature": False})
    print("Required fields present:")
    print(f"user_id: {'user_id' in decoded}")
    print(f"role: {'role' in decoded}")
    print(f"department: {'department' in decoded}")
    print(f"projects: {'projects' in decoded}")
except Exception as e:
    print(f"JWT decode error: {e}")
```

#### **Solutions**
1. **Invalid JWT format**: Work with auth team to fix token structure
2. **Missing permissions**: Update user role/department in auth system
3. **ACL filter not working**: Check `acl_filter.py` for bugs
4. **GraphQL filters not applied**: Verify filter injection in tools

---

### **Issue 5: Memory Leaks / Growing Memory Usage**

#### **Symptoms**
- Memory usage continuously growing
- System becomes slow over time
- Out of memory errors

#### **Diagnosis**
```bash
# 1. Check Redis memory usage
redis-cli info memory

# 2. Check Python process memory
ps aux | grep uvicorn | awk '{print $6}'  # RSS memory

# 3. Check scratchpad size
redis-cli dbsize

# 4. Check conversation history size
redis-cli keys "icms:messages:*" | wc -l
```

#### **Memory Analysis**
```bash
# Check largest Redis keys
redis-cli --bigkeys

# Check TTL on cached items
redis-cli keys "icms:cache:*" | head -10 | xargs -I {} redis-cli ttl {}

# Check scratchpad cleanup
redis-cli keys "icms:scratchpad:*" | head -10 | xargs -I {} redis-cli ttl {}
```

#### **Solutions**
1. **Redis memory leak**:
   ```bash
   # Clear old data
   redis-cli eval "return redis.call('del', unpack(redis.call('keys', 'icms:*')))" 0
   
   # Restart Redis
   brew services restart redis
   ```

2. **Python memory leak**:
   ```bash
   # Restart application
   pkill -f uvicorn
   make dev
   ```

3. **Scratchpad not clearing**:
   - Check TTL settings in `shared_scratchpad.py`
   - Verify cleanup logic

---

## 📊 Performance Issues

### **Issue 6: Low Cache Hit Rate (<20%)**

#### **Diagnosis**
```bash
# Check cache statistics
curl http://localhost:8000/metrics/cache

# Check similarity threshold
grep "similarity_threshold" logs/app.log | tail -5

# Check query patterns
grep "semantic_cache_miss" logs/app.log | tail -20
```

#### **Solutions**
1. **Lower similarity threshold**:
   ```bash
   export SEMANTIC_CACHE_THRESHOLD=0.75  # From 0.85
   make dev
   ```

2. **Check query variations**:
   ```python
   # Test similarity manually
   from app.core.langgraph.utils.semantic_cache import SemanticCache
   cache = SemanticCache()
   
   query1 = "Show overdue tasks"
   query2 = "Display late tasks"
   similarity = cache.calculate_similarity(query1, query2)
   print(f"Similarity: {similarity}")
   ```

3. **Warm cache with common queries**:
   ```bash
   # Create cache warming script
   common_queries=("Show project status" "Find overdue tasks" "List recent documents")
   for query in "${common_queries[@]}"; do
     curl -X POST "http://localhost:8000/chat/cache-warm" -d "{\"message\": \"$query\"}"
   done
   ```

---

### **Issue 7: Self-Healing Not Working**

#### **Symptoms**
- Errors not recovering automatically
- No retry attempts in logs
- High failure rate

#### **Diagnosis**
```bash
# Check self-healing statistics
curl http://localhost:8000/metrics/healing

# Check error classification
grep "self_healing.*non_recoverable" logs/app.log | tail -10

# Check retry attempts
grep "self_healing_retry_attempt" logs/app.log | tail -10
```

#### **Solutions**
1. **Check error patterns**:
   ```python
   from app.core.langgraph.nodes.self_healing import SelfHealingRetry
   healer = SelfHealingRetry()
   
   # Test your specific error
   error_msg = "your error message"
   is_recoverable = healer.is_recoverable_error(error_msg)
   print(f"Recoverable: {is_recoverable}")
   ```

2. **Add custom error patterns**:
   ```python
   # In self_healing.py, add to recoverable_errors list
   self.recoverable_errors.append("your_custom_error_pattern")
   ```

3. **Check retry configuration**:
   ```bash
   export ICMS_MAX_RETRIES=5  # Increase retries
   make dev
   ```

---

## 🔧 Quick Diagnostic Commands

### **System Health Check**
```bash
#!/bin/bash
echo "=== ICMS System Health Check ==="

echo "1. Application Health:"
curl -s http://localhost:8000/health | jq .

echo "2. Redis Connection:"
redis-cli ping

echo "3. GraphQL Backend:"
curl -s -X POST $GRAPHQL_ENDPOINT -d '{"query": "{ __typename }"}' | jq .

echo "4. Recent Errors:"
tail -20 logs/app.log | grep ERROR

echo "5. Performance Metrics:"
curl -s http://localhost:8000/metrics/performance | jq .

echo "6. Cache Performance:"
curl -s http://localhost:8000/metrics/cache | jq .
```

### **Performance Analysis**
```bash
#!/bin/bash
echo "=== Performance Analysis ==="

echo "Response Time Distribution:"
grep "response_time" logs/app.log | awk '{print $NF}' | sort -n | \
  awk '{
    if ($1 < 1) fast++; 
    else if ($1 < 3) medium++; 
    else slow++
  } 
  END {
    print "Fast (<1s):", fast; 
    print "Medium (1-3s):", medium; 
    print "Slow (>3s):", slow
  }'

echo "Cache Hit Rate:"
grep -c "semantic_cache_hit" logs/app.log
grep -c "semantic_cache_miss" logs/app.log

echo "Agent Distribution:"
grep "routing_decision" logs/app.log | awk '{print $NF}' | sort | uniq -c
```

---

## 📞 Escalation Guidelines

### **When to Escalate**

1. **Immediate Escalation** (Page on-call):
   - System completely down >5 minutes
   - Data corruption detected
   - Security breach suspected

2. **High Priority** (Contact within 1 hour):
   - Performance degraded >50%
   - Error rate >20%
   - Cache hit rate <10%

3. **Medium Priority** (Contact within 4 hours):
   - Intent classification accuracy <80%
   - Self-healing not working
   - Memory usage growing continuously

### **Information to Gather Before Escalating**
- System health check output
- Recent error logs (last 100 lines)
- Performance metrics
- Steps to reproduce the issue
- Impact on users

**Remember**: Most issues can be resolved with the steps in this playbook. Always try the quick fixes first before escalating.
