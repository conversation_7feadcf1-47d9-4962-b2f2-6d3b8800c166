# ICMS Multi-Agent System - Comprehensive Onboarding Guide

## 🎯 Learning Path Overview

This guide follows a structured 4-phase approach to master the ICMS Multi-Agent System:

1. **Foundation** (Days 1-2): Core architecture and data flow
2. **Implementation** (Days 3-5): Codebase deep dive and component interaction
3. **Operations** (Days 6-7): Development workflow and debugging
4. **Mastery** (Days 8-10): Advanced troubleshooting and system optimization

---

## Phase 1: Foundation - System Architecture Understanding

### 🏗️ **1.1 The Big Picture: Multi-Agent Orchestration**

The ICMS system is built on a **hierarchical supervisor pattern** where:

```
User Query → Intent Classification → Agent Selection → Specialized Execution → Response
```

**Key Insight**: Unlike traditional chatbots, this system routes queries to specialized agent teams that understand construction domain context.

### 🔄 **1.2 The 12 Refinements Flow**

Study this critical path that every query follows:

```mermaid
graph LR
    A[Query] --> B[Message Manager]
    B --> C[Semantic Cache]
    C --> D[ACL Filter]
    D --> E[Scratchpad Init]
    E --> F[Intent Classifier]
    F --> G[Score Router]
    G --> H[Agent Teams]
    H --> I[Batch Tools]
    I --> J[<PERSON>Loader]
    J --> K[Response]
```

**Start Here**: Trace a single query through this flow by reading:
1. `app/core/langgraph/graph.py` - Main orchestration
2. `app/core/langgraph/nodes/intent_classifier.py` - Decision making
3. `app/core/langgraph/subgraphs/` - Team execution

### 🎯 **1.3 Agent Team Specialization**

Each team has distinct responsibilities:

| Team | Purpose | Key Tools | When Used |
|------|---------|-----------|-----------|
| **Document Team** | File search, approval workflows, metadata | `search_documents`, `analyze_document_relationships` | "Find drawings", "Check approvals" |
| **Schedule Team** | Timeline analysis, resource planning, critical path | `get_project_schedule`, `get_critical_path` | "Show overdue tasks", "Resource conflicts" |
| **Cross-Module** | Impact analysis, dependencies, compliance | Combined tools from both teams | "What blocks this task?", "Impact of delay" |

**Action**: Read the supervisor files in `app/core/langgraph/subgraphs/` to understand team decision-making.

### 🔌 **1.4 Integration Architecture**

```
ICMS Multi-Agent System
├── Redis (Memory & Cache)
│   ├── Conversation history (last_10 + full_history_id)
│   ├── Semantic cache (FAISS embeddings)
│   └── Shared scratchpad (cross-team data)
├── GraphQL Backend (Data Source)
│   ├── Projects, Tasks, Documents
│   ├── User permissions & roles
│   └── Real-time construction data
└── OpenAI (Intelligence)
    ├── Intent classification
    ├── Entity extraction
    └── Response generation
```

**Critical Files to Study**:
- `app/core/langgraph/utils/semantic_cache.py` - How caching works
- `app/core/langgraph/tools/icms_graphql_tools.py` - Data access patterns
- `app/core/langgraph/nodes/acl_filter.py` - Security implementation

---

## Phase 2: Implementation - Codebase Deep Dive

### 📁 **2.1 Critical File Hierarchy (Study in Order)**

#### **Tier 1: Core Orchestration (Start Here)**
```
app/core/langgraph/
├── graph.py                    # 🔥 MAIN ENTRY POINT
├── schemas/graph.py            # State management
└── nodes/intent_classifier.py  # Decision engine
```

#### **Tier 2: Agent Implementation**
```
app/core/langgraph/subgraphs/
├── document_team.py           # Document specialists
├── schedule_team.py           # Schedule specialists
└── cross_module.py            # Cross-team coordination
```

#### **Tier 3: Performance & Reliability**
```
app/core/langgraph/utils/
├── message_manager.py         # Memory optimization
├── semantic_cache.py          # Response caching
├── dataloader.py             # N+1 elimination
└── timeout_wrapper.py        # SLA management
```

### 🧠 **2.2 Key Classes to Master**

#### **IcmsState (The System's Memory)**
```python
# Location: app/schemas/graph.py
class IcmsState(TypedDict):
    messages: List[BaseMessage]           # Conversation
    intent: str                          # Classified intent
    entities: Dict                       # Extracted data
    user_permissions: Dict               # Security context
    scratchpad: SharedScratchpad         # Cross-team memory
    confidence_score: float              # Classification confidence
```

**Why Critical**: Every agent reads/writes to this state. Understanding it is key to debugging.

#### **IntentClassifier (The Brain)**
```python
# Location: app/core/langgraph/nodes/intent_classifier.py
class IntentClassificationResult(BaseModel):
    intent: str                          # DOC_INTENT, SCHED_INTENT, CROSS_INTENT
    confidence: float                    # 0.0-1.0 (≤0.7 triggers clarification)
    entities: Dict                       # Extracted project_id, date_range, etc.
    requires_clarification: bool         # Auto-routing decision
```

**Debug Tip**: When queries go to wrong agents, check confidence scores and entity extraction here.

#### **SemanticCache (The Performance Booster)**
```python
# Location: app/core/langgraph/utils/semantic_cache.py
class SemanticCache:
    async def check_cache(query: str) -> Optional[str]  # 30-50% hit rate
    async def store_cache(query: str, response: str)    # Embedding storage
```

**Performance Impact**: This single class provides 30-50% instant responses.

### 🔧 **2.3 LangGraph Orchestration Patterns**

#### **Conditional Routing Pattern**
```python
# How agents are selected based on intent and confidence
def route_to_agent(state: IcmsState) -> str:
    if state["requires_clarify"]:
        return "clarification"
    elif state["intent"] == "DOC_INTENT":
        return "document_team"
    elif state["intent"] == "SCHED_INTENT":
        return "schedule_team"
    else:
        return "cross_module"
```

#### **Tool Execution Pattern**
```python
# How tools are called with timeout and error handling
@with_timeout(3.0)
async def execute_tool(tool_name: str, **kwargs):
    # ACL filtering applied automatically
    # DataLoader batching happens here
    # Results stored in scratchpad
```

**Study**: `app/core/langgraph/subgraphs/document_team.py` for complete examples.

---

## Phase 3: Operations - Development Workflow

### 🛠️ **3.1 Complete Local Setup (Step-by-Step)**

#### **Environment Setup**
```bash
# 1. Clone and setup
git clone <repo>
cd icms-multi-agent
uv sync

# 2. Redis (Required for all 12 refinements)
brew install redis
brew services start redis

# 3. Environment configuration
cp .env.example .env.development
```

#### **Critical Environment Variables**
```bash
# Core (Required)
AZURE_OPENAI_ENDPOINT="your-endpoint"
AZURE_OPENAI_API_KEY="your-key"
REDIS_URL="redis://localhost:6379/0"
GRAPHQL_ENDPOINT="http://localhost:3000/graphql"

# Performance Tuning
SEMANTIC_CACHE_THRESHOLD=0.85
ICMS_CONFIDENCE_THRESHOLD=0.7
ICMS_TIMEOUT_SECONDS=3.0

# Debug Mode
LOG_LEVEL=DEBUG
ICMS_DEBUG_MODE=true
```

#### **Verification Steps**
```bash
# 1. Test Redis connection
redis-cli ping  # Should return PONG

# 2. Test GraphQL backend
curl -X POST http://localhost:3000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "{ __schema { types { name } } }"}'

# 3. Start ICMS system
make dev

# 4. Health check
curl http://localhost:8000/health
```

### 🔍 **3.2 Component Testing Strategies**

#### **Test Individual Refinements**
```python
# Test semantic cache
from app.core.langgraph.utils.semantic_cache import SemanticCache
cache = SemanticCache()
await cache.store_cache("test query", "test response")
result = await cache.check_cache("similar test query")

# Test intent classification
from app.core.langgraph.nodes.intent_classifier import classify_intent_node
state = {"messages": [HumanMessage(content="Show overdue tasks")]}
result = await classify_intent_node(state)
print(f"Intent: {result['intent']}, Confidence: {result['confidence_score']}")

# Test ACL filtering
from app.core.langgraph.nodes.acl_filter import acl_filter_node
state = {"jwt_token": "your-test-token"}
result = await acl_filter_node(state)
print(f"Permissions: {result['user_permissions']}")
```

#### **End-to-End Query Tracing**
```python
# Enable debug logging
import logging
logging.getLogger("app.core.langgraph").setLevel(logging.DEBUG)

# Trace a complete query
from app.core.langgraph.graph import get_response
response = await get_response(
    session_id="test-session",
    message="Find structural drawings for foundation",
    jwt="your-token"
)
```

### 🐛 **3.3 Debugging Multi-Agent Interactions**

#### **Common Debug Scenarios**

1. **Wrong Agent Selected**
   - Check: `intent_classifier.py` confidence scores
   - Look for: Entity extraction accuracy
   - Fix: Adjust `ICMS_CONFIDENCE_THRESHOLD`

2. **Slow Responses**
   - Check: Cache hit rates in logs
   - Look for: N+1 queries in GraphQL
   - Fix: Verify DataLoader is working

3. **Permission Errors**
   - Check: `acl_filter.py` JWT decoding
   - Look for: User role/department mapping
   - Fix: Verify GraphQL ACL filters

4. **Self-Healing Not Working**
   - Check: Error classification in `self_healing.py`
   - Look for: Retry attempt logs
   - Fix: Verify error patterns match recoverable_errors

#### **Debug Logging Patterns**
```python
# Key log messages to watch for
"intent_classification_completed"     # Intent decision made
"semantic_cache_hit"                 # Cache working
"acl_filter_applied"                 # Security working
"batch_operation_started"            # Performance optimization
"self_healing_retry_attempt"         # Error recovery
```

---

## Phase 4: Mastery - Advanced Operations

### 📊 **4.1 System Health Monitoring**

#### **Key Metrics Dashboard**
```bash
# Performance metrics
curl http://localhost:8000/metrics/performance
{
  "avg_response_time": 0.8,
  "cache_hit_rate": 0.42,
  "intent_accuracy": 0.91,
  "sla_compliance": 0.95
}

# Agent performance
curl http://localhost:8000/metrics/agents
{
  "document_team": {"success_rate": 0.94, "avg_time": 0.6},
  "schedule_team": {"success_rate": 0.96, "avg_time": 0.5},
  "cross_module": {"success_rate": 0.89, "avg_time": 1.2}
}

# Self-healing stats
curl http://localhost:8000/metrics/healing
{
  "recovery_rate": 0.83,
  "retry_attempts": 156,
  "successful_recoveries": 129
}
```

#### **Critical Alerts to Set Up**
- Response time > 3 seconds (SLA violation)
- Cache hit rate < 20% (Performance degradation)
- Intent accuracy < 85% (Classification issues)
- Error recovery rate < 70% (System instability)

### 🚨 **4.2 Common Issues & Solutions**

#### **High-Priority Issues**

| Issue | Symptoms | Root Cause | Solution |
|-------|----------|------------|----------|
| **Slow Responses** | >3s response time | Cache misses, N+1 queries | Check Redis, verify DataLoader |
| **Wrong Agent** | Incorrect responses | Low confidence, poor entity extraction | Retrain intent classifier |
| **Permission Errors** | 403 errors | JWT issues, ACL misconfiguration | Verify token format, check GraphQL filters |
| **Memory Leaks** | Increasing RAM usage | Scratchpad not clearing | Check Redis TTL settings |

#### **Performance Optimization Checklist**
```bash
# 1. Cache Performance
redis-cli info memory  # Check Redis memory usage
redis-cli dbsize       # Check cache size

# 2. GraphQL Optimization
# Monitor for N+1 queries in logs
grep "dataloader_batch" logs/app.log

# 3. Intent Classification
# Check confidence score distribution
grep "confidence_score" logs/app.log | awk '{print $NF}' | sort -n

# 4. Self-Healing Effectiveness
# Monitor recovery rates
grep "self_healing_retry_success" logs/app.log | wc -l
```

### 🏗️ **4.3 Construction Domain Context**

#### **Key Construction Workflows**
1. **Document Management**: RFIs, submittals, drawings, specifications
2. **Schedule Management**: Critical path, resource allocation, milestone tracking
3. **Compliance Tracking**: Permits, inspections, safety requirements
4. **Change Management**: Change orders, impact analysis, approval workflows

#### **ICMS Data Model Understanding**
```python
# Project hierarchy
Project
├── Tasks (WBS structure)
│   ├── Dependencies (predecessors/successors)
│   ├── Resources (labor, equipment, materials)
│   └── Progress (% complete, actual vs planned)
├── Documents
│   ├── Drawings (architectural, structural, MEP)
│   ├── Specifications (technical requirements)
│   └── Submittals (product data, shop drawings)
└── Compliance
    ├── Permits (building, environmental)
    ├── Inspections (quality, safety)
    └── Certifications (material, testing)
```

#### **User Role Matrix**
| Role | Document Access | Schedule Access | Typical Queries |
|------|----------------|-----------------|-----------------|
| **Contractor** | Approved only | Assigned tasks | "My tasks this week" |
| **Engineer** | Draft + Approved | Department tasks | "Structural drawings status" |
| **Project Manager** | All statuses | All project tasks | "Critical path analysis" |
| **Admin** | All documents | All schedules | "System performance" |

### 🚀 **4.4 Scaling & Production Considerations**

#### **Performance Scaling**
- **Redis Cluster**: For >1000 concurrent users
- **Load Balancing**: Multiple ICMS instances
- **GraphQL Optimization**: Connection pooling, query complexity limits
- **Cache Warming**: Pre-populate common queries

#### **Reliability Scaling**
- **Health Checks**: Kubernetes liveness/readiness probes
- **Circuit Breakers**: Prevent cascade failures
- **Graceful Degradation**: Fallback to basic responses
- **Monitoring**: Comprehensive observability stack

---

## 🎯 Quick Reference Cheat Sheet

### **Emergency Debugging Commands**
```bash
# Check system health
curl http://localhost:8000/health

# View recent errors
tail -f logs/app.log | grep ERROR

# Check Redis connection
redis-cli ping

# Test GraphQL backend
curl -X POST $GRAPHQL_ENDPOINT -d '{"query": "{ __typename }"}'

# Restart with debug logging
LOG_LEVEL=DEBUG make dev
```

### **Key File Quick Access**
- **Main orchestration**: `app/core/langgraph/graph.py`
- **Intent classification**: `app/core/langgraph/nodes/intent_classifier.py`
- **Performance cache**: `app/core/langgraph/utils/semantic_cache.py`
- **Security**: `app/core/langgraph/nodes/acl_filter.py`
- **Configuration**: `app/core/config.py`

### **Critical Log Messages**
- `"intent_classification_completed"` - Decision made
- `"semantic_cache_hit"` - Performance win
- `"self_healing_retry_attempt"` - Error recovery
- `"acl_filter_applied"` - Security working
- `"batch_operation_completed"` - Optimization active

**Next Steps**: Start with Phase 1, spend 2 days on each phase, and refer back to this guide as your system reference manual.
