# ICMS Multi-Agent System - Complete System Reference

## 🏗️ System Architecture Overview

This document serves as the definitive technical reference for the ICMS Multi-Agent System architecture, components, and data flows.

---

## 📊 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    ICMS Multi-Agent System                      │
├─────────────────────────────────────────────────────────────────┤
│  FastAPI Gateway                                                │
│  ├── Authentication (JWT)                                       │
│  ├── Rate Limiting                                              │
│  └── Request Routing                                            │
├─────────────────────────────────────────────────────────────────┤
│  LangGraph Orchestration Engine                                 │
│  ├── Message Manager (Refinement #1)                           │
│  ├── Semantic Cache (Refinement #2)                            │
│  ├── ACL Filter (Refinement #3)                                │
│  ├── Intent Classifier (Refinements #4, #6)                   │
│  ├── Score-Based Router (Refinement #10)                      │
│  ├── Shared Scratchpad (Refinement #11)                       │
│  └── Self-Healing Retry (Refinement #12)                      │
├─────────────────────────────────────────────────────────────────┤
│  Specialized Agent Teams                                        │
│  ├── Document Team Sub-Graph                                   │
│  ├── Schedule Team Sub-Graph                                   │
│  └── Cross-Module Coordinator                                  │
├─────────────────────────────────────────────────────────────────┤
│  Performance Layer                                              │
│  ├── Batch Tools (Refinement #7)                              │
│  ├── DataLoader Pattern (Refinement #8)                       │
│  ├── Timeout Management (Refinement #9)                       │
│  └── Pydantic Models (Refinement #5)                          │
├─────────────────────────────────────────────────────────────────┤
│  External Integrations                                          │
│  ├── Redis (Memory, Cache, Scratchpad)                        │
│  ├── FAISS (Vector Similarity Search)                         │
│  ├── OpenAI (Language Model)                                  │
│  └── ICMS GraphQL Backend (Data Source)                       │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔄 Complete Data Flow

### **1. Request Processing Pipeline**

```
User Query
    ↓
[FastAPI Gateway]
    ├── JWT Authentication
    ├── Rate Limiting Check
    └── Request Validation
    ↓
[Message Manager] (Refinement #1)
    ├── Extract last_10 messages
    ├── Store full_history_id in Redis
    └── Optimize conversation state
    ↓
[Semantic Cache] (Refinement #2)
    ├── Generate query embedding (sentence-transformers)
    ├── Search FAISS index (threshold: 0.85)
    └── Return cached response OR continue
    ↓
[ACL Filter] (Refinement #3)
    ├── Decode JWT token
    ├── Extract user permissions (role, department, projects)
    └── Store security context in state
    ↓
[Shared Scratchpad Init] (Refinement #11)
    ├── Initialize Redis scratchpad namespace
    ├── Load existing session context
    └── Prepare cross-module coordination
    ↓
[Intent Classifier] (Refinements #4, #6)
    ├── Analyze conversation history (-10 messages)
    ├── OpenAI function calling (multilingual)
    ├── Extract intent + entities + confidence
    └── Route to clarification if confidence ≤ 0.7
    ↓
[Score-Based Router] (Refinement #10)
    ├── Multi-factor agent scoring
    ├── Keyword + entity + context analysis
    └── Select best agent (90% accuracy)
    ↓
[Agent Team Execution]
    ├── Document Team (search, analysis, metadata)
    ├── Schedule Team (planning, critical path, resources)
    └── Cross-Module (impact analysis, dependencies)
    ↓
[Performance Optimizations] (Refinements #7, #8, #9)
    ├── Batch Tools (3-5× faster comparisons)
    ├── DataLoader Pattern (N+1 elimination)
    └── Timeout Management (3s SLA)
    ↓
[GraphQL Backend Integration]
    ├── Apply ACL filters to queries
    ├── Execute optimized GraphQL operations
    └── Return filtered, validated data
    ↓
[Response Generation]
    ├── Process results with Pydantic models
    ├── Update shared scratchpad
    └── Store in semantic cache
    ↓
[Self-Healing] (Refinement #12)
    ├── Monitor for 5xx errors
    ├── Apply adaptive retry strategies
    └── 80% recovery rate
    ↓
User Response
```

---

## 🧩 Component Deep Dive

### **Core Components**

#### **1. Message Manager (`app/core/langgraph/utils/message_manager.py`)**
```python
class MessageManager:
    async def prepare_state_messages(session_id: str, messages: List) -> Tuple[List, str]
    # Returns: (last_10_messages, full_history_id)
    # Impact: 80% reduction in prompt size
```

#### **2. Semantic Cache (`app/core/langgraph/utils/semantic_cache.py`)**
```python
class SemanticCache:
    async def check_cache(query: str) -> Optional[str]
    async def store_cache(query: str, response: str) -> bool
    # Model: sentence-transformers/all-MiniLM-L6-v2
    # Threshold: 0.85 similarity
    # Hit Rate: 30-50%
```

#### **3. ACL Filter (`app/core/langgraph/nodes/acl_filter.py`)**
```python
class ACLFilter:
    def decode_jwt(token: str) -> Dict
    def apply_acl_filters(base_filters: Dict, permissions: Dict) -> Dict
    # Security: Row-level filtering on all GraphQL queries
```

#### **4. Intent Classifier (`app/core/langgraph/nodes/intent_classifier.py`)**
```python
class IntentClassificationResult(BaseModel):
    intent: str                    # DOC_INTENT, SCHED_INTENT, CROSS_INTENT
    confidence: float              # 0.0-1.0 (≤0.7 triggers clarification)
    entities: Dict                 # Extracted project_id, date_range, etc.
    requires_clarification: bool   # Auto-routing decision
```

### **Agent Teams**

#### **Document Team (`app/core/langgraph/subgraphs/document_team.py`)**
- **Purpose**: Document search, analysis, approval workflows
- **Tools**: `search_documents`, `analyze_document_relationships`, `batch_search_documents`
- **Specialization**: RFIs, submittals, drawings, specifications
- **ACL**: Status-based filtering (draft, approved, confidential)

#### **Schedule Team (`app/core/langgraph/subgraphs/schedule_team.py`)**
- **Purpose**: Timeline analysis, resource planning, critical path
- **Tools**: `get_project_schedule`, `get_critical_path`, `batch_get_schedules`
- **Specialization**: Task management, milestone tracking, resource allocation
- **ACL**: Assignment-based filtering (user tasks, department tasks)

#### **Cross-Module Team (`app/core/langgraph/subgraphs/cross_module.py`)**
- **Purpose**: Impact analysis, dependency mapping, compliance
- **Tools**: Combined tools from both teams + `batch_analyze_dependencies`
- **Specialization**: Change management, risk assessment, workflow optimization
- **ACL**: Combined permissions from both domains

### **Performance Components**

#### **Batch Tools (`app/core/langgraph/tools/batch_tools.py`)**
```python
@tool
async def batch_search_documents(queries: List[Dict]) -> dict
async def batch_get_schedules(project_ids: List[str]) -> dict
async def batch_compare_tasks(task_ids: List[str]) -> dict
# Performance: 3-5× faster for comparison queries
```

#### **DataLoader (`app/core/langgraph/utils/dataloader.py`)**
```python
class GraphQLDataLoader:
    async def load_projects(project_ids: List[str]) -> List[Dict]
    async def load_tasks(task_ids: List[str]) -> List[Dict]
    async def load_documents(document_ids: List[str]) -> List[Dict]
# Benefit: Eliminates N+1 queries, 60% faster GraphQL
```

#### **Timeout Wrapper (`app/core/langgraph/utils/timeout_wrapper.py`)**
```python
@with_timeout(timeout_seconds=3.0)
@with_retry_timeout(timeout_seconds=3.0, max_retries=2)
# SLA: 95% compliance with 3-second timeout
```

---

## 📊 State Management

### **IcmsState Schema (`app/schemas/graph.py`)**
```python
class IcmsState(TypedDict):
    # Core conversation
    messages: List[BaseMessage]              # Conversation history
    full_history_id: str                     # Redis key for complete history
    
    # Intent classification
    intent: str                              # DOC_INTENT, SCHED_INTENT, CROSS_INTENT
    entities: Dict                           # Extracted entities
    confidence_score: float                  # Classification confidence
    requires_clarify: bool                   # Clarification needed
    
    # Security context
    user_permissions: Dict                   # Role, department, projects
    jwt_token: str                          # Authentication token
    
    # Performance tracking
    agent_scores: Dict                       # Score-based routing results
    selected_agent: str                      # Chosen agent team
    
    # Cross-module coordination
    scratchpad: SharedScratchpad            # Shared memory
    scratchpad_session_id: str              # Session identifier
    
    # Error handling
    last_error: Optional[str]               # Last error for self-healing
    healing_attempt: int                    # Retry attempt number
    healing_strategy: str                   # Applied healing strategy
```

---

## 🔧 Configuration Reference

### **Environment Variables**

#### **Core System**
```bash
# Application
ENVIRONMENT=development|staging|production
PROJECT_NAME="ICMS Multi-Agent System"
LOG_LEVEL=DEBUG|INFO|WARNING|ERROR

# OpenAI Integration
AZURE_OPENAI_ENDPOINT="https://your-endpoint.openai.azure.com/"
AZURE_OPENAI_API_KEY="your-api-key"
AZURE_OPENAI_DEPLOYMENT_NAME="gpt-4"
AZURE_OPENAI_API_VERSION="2024-02-15-preview"
```

#### **Performance Tuning**
```bash
# Message Management (Refinement #1)
MESSAGE_HISTORY_LIMIT=10                    # last_10 optimization
REDIS_TTL=3600                             # Full history TTL

# Semantic Cache (Refinement #2)
SEMANTIC_CACHE_THRESHOLD=0.85              # Similarity threshold
SENTENCE_TRANSFORMER_MODEL="all-MiniLM-L6-v2"
FAISS_INDEX_SIZE=10000                     # Vector index capacity

# Intent Classification (Refinement #4)
ICMS_CONFIDENCE_THRESHOLD=0.7              # Clarification threshold

# Timeout Management (Refinement #9)
ICMS_TIMEOUT_SECONDS=3.0                   # SLA timeout
ICMS_MAX_RETRIES=3                         # Self-healing retries

# Batch Operations (Refinement #7)
BATCH_SIZE_LIMIT=100                       # Max batch size
DATALOADER_TIMEOUT_MS=10                   # DataLoader timeout
```

#### **Security & Integration**
```bash
# Security
JWT_SECRET_KEY="your-secret-key"
ACL_STRICT_MODE=true
AUDIT_LOGGING=true

# External Services
REDIS_URL="redis://localhost:6379/0"
GRAPHQL_ENDPOINT="http://localhost:3000/graphql"
GRAPHQL_TIMEOUT=30
GRAPHQL_RETRY_ATTEMPTS=2
```

---

## 📈 Monitoring & Metrics

### **Key Performance Indicators**

| Metric | Target | Critical Threshold | Monitoring Endpoint |
|--------|--------|-------------------|-------------------|
| **Response Time** | <1s avg | >3s | `/metrics/performance` |
| **Cache Hit Rate** | 30-50% | <20% | `/metrics/cache` |
| **Intent Accuracy** | >90% | <85% | `/metrics/agents` |
| **Error Recovery** | >80% | <70% | `/metrics/healing` |
| **SLA Compliance** | >95% | <90% | `/metrics/sla` |
| **Memory Usage** | <2GB | >4GB | `/health` |

### **Health Check Endpoints**
```bash
# System health
GET /health
{
  "status": "healthy",
  "version": "1.0.0",
  "uptime": 3600,
  "dependencies": {
    "redis": "connected",
    "graphql": "connected",
    "openai": "connected"
  }
}

# Performance metrics
GET /metrics/performance
{
  "avg_response_time": 0.8,
  "p95_response_time": 2.1,
  "total_requests": 1500,
  "error_rate": 0.02
}

# Cache statistics
GET /metrics/cache
{
  "hit_rate": 0.42,
  "total_cached_queries": 650,
  "faiss_index_size": 650,
  "avg_similarity_score": 0.87
}
```

---

## 🚀 Deployment Architecture

### **Production Deployment**
```yaml
# docker-compose.production.yml
version: '3.8'
services:
  icms-agent:
    image: icms-multi-agent:latest
    replicas: 3
    environment:
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis-cluster:6379
    depends_on:
      - redis-cluster
      - prometheus
      
  redis-cluster:
    image: redis:alpine
    command: redis-server --appendonly yes
    volumes:
      - redis-data:/data
      
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
      
  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
```

### **Scaling Considerations**
- **Horizontal Scaling**: Multiple ICMS instances behind load balancer
- **Redis Scaling**: Redis Cluster for high availability
- **Cache Optimization**: Distributed caching with consistent hashing
- **GraphQL Optimization**: Connection pooling and query complexity limits

---

## 🔍 Debugging Reference

### **Log Patterns to Monitor**
```bash
# Critical success indicators
"intent_classification_completed"          # Decision engine working
"semantic_cache_hit"                      # Performance optimization
"acl_filter_applied"                      # Security working
"batch_operation_completed"               # Performance optimization
"self_healing_retry_success"              # Error recovery

# Warning indicators
"confidence_score.*0\.[0-6]"              # Low confidence classifications
"semantic_cache_miss"                     # Cache not helping
"timeout_exceeded"                        # Performance issues
"self_healing_retry_attempt"              # Errors occurring

# Error indicators
"intent_classification_failed"            # Core functionality broken
"acl_filter_error"                        # Security issues
"graphql_query_failed"                    # Data access problems
"self_healing_max_retries_exceeded"       # System instability
```

### **Common Debug Commands**
```bash
# Real-time log monitoring
tail -f logs/app.log | grep -E "(ERROR|WARNING|intent_classification|semantic_cache)"

# Performance analysis
grep "response_time" logs/app.log | awk '{print $NF}' | sort -n | tail -20

# Cache effectiveness
grep -c "semantic_cache_hit" logs/app.log
grep -c "semantic_cache_miss" logs/app.log

# Agent distribution
grep "routing_decision" logs/app.log | awk '{print $NF}' | sort | uniq -c
```

This reference guide provides the complete technical foundation for understanding, operating, and troubleshooting the ICMS Multi-Agent System.
