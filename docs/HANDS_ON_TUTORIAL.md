# ICMS Multi-Agent System - Hands-On Tutorial

## 🎯 Practical Exercises for System Mastery

This tutorial provides hands-on exercises to build practical expertise with the ICMS Multi-Agent System. Complete these exercises in order to gain operational proficiency.

---

## Exercise 1: Trace a Query End-to-End (30 minutes)

### **Objective**: Understand how a single query flows through all 12 refinements

### **Setup**
```bash
# 1. Enable debug logging
export LOG_LEVEL=DEBUG
export ICMS_DEBUG_MODE=true

# 2. Start the system
make dev

# 3. Open a second terminal for log monitoring
tail -f logs/app.log | grep -E "(intent_classification|semantic_cache|acl_filter|scratchpad|batch_operation)"
```

### **Exercise Steps**

#### **Step 1: Send a Test Query**
```bash
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMTIzIiwicm9sZSI6ImVuZ2luZWVyIiwiZGVwYXJ0bWVudCI6InN0cnVjdHVyYWwifQ.example" \
  -d '{
    "message": "Show me overdue structural tasks for Project Alpha",
    "schedule_id": "project-alpha-123"
  }'
```

#### **Step 2: Analyze the Log Flow**
Watch for these log entries in sequence:

1. **Message Manager**: `"message_optimization_applied"`
2. **Semantic Cache**: `"semantic_cache_check"` (miss on first run)
3. **ACL Filter**: `"acl_filter_applied"` with user permissions
4. **Scratchpad**: `"shared_scratchpad_initialized"`
5. **Intent Classifier**: `"intent_classification_completed"` with confidence
6. **Score Router**: `"score_based_routing_completed"`
7. **Agent Execution**: `"schedule_team_processing"`
8. **Batch Tools**: `"batch_operation_started"`
9. **DataLoader**: `"dataloader_batch_completed"`
10. **Response**: `"semantic_cache_stored"`

#### **Step 3: Send the Same Query Again**
```bash
# Same curl command - should hit semantic cache
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiMTIzIiwicm9sZSI6ImVuZ2luZWVyIiwiZGVwYXJ0bWVudCI6InN0cnVjdHVyYWwifQ.example" \
  -d '{
    "message": "Show me overdue structural tasks for Project Alpha",
    "schedule_id": "project-alpha-123"
  }'
```

**Expected**: `"semantic_cache_hit"` log entry and sub-second response.

### **Learning Outcomes**
- ✅ Understand the complete query flow
- ✅ See semantic caching in action
- ✅ Observe ACL filtering working
- ✅ Recognize performance optimizations

---

## Exercise 2: Test Intent Classification Edge Cases (20 minutes)

### **Objective**: Understand how confidence scoring and clarification work

### **Test Cases**

#### **High Confidence Query (Should Route Directly)**
```bash
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -d '{"message": "Find all structural drawings for foundation work"}'
```
**Expected**: `confidence_score > 0.7`, routes to `document_team`

#### **Low Confidence Query (Should Trigger Clarification)**
```bash
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -d '{"message": "What about the thing we discussed yesterday?"}'
```
**Expected**: `confidence_score ≤ 0.7`, routes to `clarification`

#### **Multilingual Query (Should Work Seamlessly)**
```bash
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -d '{"message": "Tunjukkan tugasan yang tertunggak bulan ini"}'
```
**Expected**: Same performance as English, routes to `schedule_team`

#### **Cross-Module Query (Should Use Score-Based Router)**
```bash
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -d '{"message": "What documents are blocking the foundation tasks scheduled for next week?"}'
```
**Expected**: Routes to `cross_module` team

### **Debug Analysis**
```bash
# Check confidence scores
grep "confidence_score" logs/app.log | tail -10

# Check routing decisions
grep "routing_decision" logs/app.log | tail -10

# Check clarification triggers
grep "requires_clarify" logs/app.log | tail -10
```

---

## Exercise 3: Performance Optimization Testing (25 minutes)

### **Objective**: Verify all performance refinements are working

### **Batch Operations Test**
```bash
# Send multiple related queries to trigger batching
for i in {1..5}; do
  curl -X POST "http://localhost:8000/chat/tutorial-session-$i" \
    -d "{\"message\": \"Compare progress of tasks in Project Alpha vs Project Beta\"}" &
done
wait
```

**Expected**: `"batch_operation_started"` logs showing concurrent processing

### **DataLoader N+1 Elimination Test**
```bash
# Query that would normally cause N+1 problem
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -d '{"message": "Show me all tasks and their related documents for Project Alpha"}'
```

**Expected**: `"dataloader_batch_completed"` instead of multiple individual queries

### **Timeout Management Test**
```bash
# Simulate slow backend (if you have control over GraphQL backend)
# Or check timeout handling in logs
grep "tool_timeout" logs/app.log | tail -5
```

### **Cache Performance Analysis**
```bash
# Check cache statistics
curl http://localhost:8000/metrics/cache

# Expected output:
{
  "total_cached_queries": 150,
  "faiss_index_size": 150,
  "hit_rate": 0.42,
  "avg_similarity_score": 0.87
}
```

---

## Exercise 4: Security & ACL Testing (20 minutes)

### **Objective**: Verify row-level security is working correctly

### **Different User Roles Test**

#### **Contractor Role (Limited Access)**
```bash
# JWT with contractor role
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiNDU2Iiwicm9sZSI6ImNvbnRyYWN0b3IiLCJwcm9qZWN0cyI6WyJhbHBoYSJdLCJkZXBhcnRtZW50IjoiY29uc3RydWN0aW9uIn0.example" \
  -d '{"message": "Show me all project documents"}'
```
**Expected**: Only approved documents from assigned projects

#### **Project Manager Role (Full Access)**
```bash
# JWT with PM role
curl -X POST "http://localhost:8000/chat/tutorial-session" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoiNzg5Iiwicm9sZSI6InByb2plY3RfbWFuYWdlciIsInByb2plY3RzIjpbImFscGhhIiwiYmV0YSJdLCJkZXBhcnRtZW50IjoibWFuYWdlbWVudCJ9.example" \
  -d '{"message": "Show me all project documents"}'
```
**Expected**: All documents including drafts and confidential

### **ACL Filter Verification**
```bash
# Check ACL filtering logs
grep "acl_filter_applied" logs/app.log | tail -5

# Check permission extraction
grep "user_permissions" logs/app.log | tail -3
```

---

## Exercise 5: Error Handling & Self-Healing (15 minutes)

### **Objective**: Test the self-healing retry mechanism

### **Simulate Recoverable Error**
```bash
# If you can control the GraphQL backend, return 5xx errors
# Otherwise, check existing self-healing logs
grep "self_healing_retry_attempt" logs/app.log | tail -10
```

### **Test Error Classification**
```python
# In Python console
from app.core.langgraph.nodes.self_healing import SelfHealingRetry

healer = SelfHealingRetry()

# Test error classification
print(healer.is_recoverable_error("500 Internal Server Error"))  # True
print(healer.is_recoverable_error("404 Not Found"))              # False
print(healer.is_recoverable_error("Connection timeout"))         # True
```

### **Monitor Recovery Success**
```bash
# Check recovery statistics
curl http://localhost:8000/metrics/healing

# Expected output:
{
  "recovery_rate": 0.83,
  "total_attempts": 45,
  "successful_recoveries": 37,
  "failed_recoveries": 8
}
```

---

## Exercise 6: Cross-Module Coordination (25 minutes)

### **Objective**: Test shared scratchpad and cross-team coordination

### **Multi-Step Query Sequence**
```bash
# Step 1: Document query (stores context in scratchpad)
curl -X POST "http://localhost:8000/chat/coordination-test" \
  -d '{"message": "Find the latest structural drawings for foundation work"}'

# Step 2: Schedule query (should access document context)
curl -X POST "http://localhost:8000/chat/coordination-test" \
  -d '{"message": "What tasks depend on those drawings?"}'

# Step 3: Cross-module analysis (should use both contexts)
curl -X POST "http://localhost:8000/chat/coordination-test" \
  -d '{"message": "If those drawings are delayed, what is the impact on the schedule?"}'
```

### **Scratchpad Verification**
```bash
# Check scratchpad usage
grep "scratchpad_write" logs/app.log | tail -5
grep "scratchpad_read" logs/app.log | tail -5

# Check cross-module coordination
grep "cross_module_coordination" logs/app.log | tail -3
```

---

## Exercise 7: Production Readiness Check (20 minutes)

### **Objective**: Verify system is production-ready

### **Health Check Comprehensive**
```bash
# System health
curl http://localhost:8000/health

# Performance metrics
curl http://localhost:8000/metrics/performance

# Agent statistics
curl http://localhost:8000/metrics/agents

# Cache performance
curl http://localhost:8000/metrics/cache

# SLA compliance
curl http://localhost:8000/metrics/sla
```

### **Load Testing (Basic)**
```bash
# Simple load test
for i in {1..20}; do
  curl -X POST "http://localhost:8000/chat/load-test-$i" \
    -d '{"message": "Show me project status"}' &
done
wait

# Check performance under load
curl http://localhost:8000/metrics/performance
```

### **Configuration Validation**
```bash
# Check all critical environment variables
python -c "
from app.core.config import settings
print(f'Redis: {settings.REDIS_URL}')
print(f'GraphQL: {settings.GRAPHQL_ENDPOINT}')
print(f'Cache Threshold: {settings.SEMANTIC_CACHE_THRESHOLD}')
print(f'Confidence Threshold: {settings.ICMS_CONFIDENCE_THRESHOLD}')
"
```

---

## 🎯 Completion Checklist

After completing all exercises, you should be able to:

- ✅ **Trace any query** through the complete 12-refinement flow
- ✅ **Debug intent classification** issues and confidence problems
- ✅ **Verify performance optimizations** are working (cache, batch, DataLoader)
- ✅ **Test security controls** with different user roles
- ✅ **Understand error recovery** and self-healing mechanisms
- ✅ **Monitor cross-module coordination** through shared scratchpad
- ✅ **Assess production readiness** with comprehensive health checks

## 🚀 Next Steps

1. **Set up monitoring dashboards** using the metrics endpoints
2. **Create custom evaluation scenarios** for your specific use cases
3. **Implement additional batch tools** for domain-specific operations
4. **Optimize cache thresholds** based on your query patterns
5. **Scale the system** for your expected load

**Congratulations!** You now have hands-on experience with all critical aspects of the ICMS Multi-Agent System.
