graph TD
A[User Query] --> B[FastAPI Gateway]
B --> C[Message Manager]

    %% Refinement 1: Message Management
    C --> C1[Extract last_10 messages]
    C1 --> C2[Store full_history in Redis<br/>Key: full_history_id]
    C2 --> C3[Prepare optimized state]

    %% Refinement 2: Semantic Cache
    C3 --> D[Semantic Cache Check]
    D --> D1[sentence-transformers<br/>Query Embedding]
    D1 --> D2[FAISS Similarity Search<br/>Threshold: 0.85]
    D2 -->|Cache Hit| Z[Return Cached Response]
    D2 -->|Cache Miss| E[Continue Processing]

    %% Refinement 3: ACL Filter
    E --> F[ACL Filter Node]
    F --> F1[Decode JWT Token]
    F1 --> F2[Extract Permissions:<br/>• Role & Department<br/>• Project Access<br/>• Document/Schedule Rights]
    F2 --> F3[Store user_permissions in state]

    %% Refinement 11: Shared Scratchpad
    F3 --> G[Shared Scratchpad Init]
    G --> G1[Initialize Redis Scratchpad<br/>Session-based namespaces]
    G1 --> G2[Store scratchpad reference in state]

    %% Refinement 4 & 6: Enhanced Intent Classification
    G2 --> H[Intent Classifier Node]
    H --> H1[Analyze Chat History<br/>-10 messages context]
    H1 --> H2[OpenAI Function Calling<br/>Multilingual Support]
    H2 --> H3[Extract Intent + Entities<br/>with Confidence Score]
    H3 --> H4{Confidence ≤ 0.7?}
    H4 -->|Yes| I[Clarification Node]
    H4 -->|No| J[Score-Based Router]

    %% Refinement 10: Score-Based Router
    J --> J1[AgentScorer Analysis]
    J1 --> J2[Multi-factor Scoring:<br/>• Keyword matching<br/>• Entity analysis<br/>• Context awareness<br/>• Confidence adjustment]
    J2 --> J3[Select Best Agent<br/>90% accuracy]

    J3 --> K{Routing Decision}

    %% Team Routing
    K -->|DOC_INTENT| L[Document Team]
    K -->|SCHED_INTENT| M[Schedule Team]
    K -->|CROSS_INTENT| N[Cross-Module Team]
    K -->|Error/Unknown| O[Self-Healing Retry]

    %% Document Team with Refinements
    L --> L1[Document Supervisor<br/>+ Scratchpad Access]
    L1 --> L2[Route to Specialist]
    L2 --> L3[Document Tools with:<br/>• ACL Filtering<br/>• Timeout Management<br/>• DataLoader Pattern]

    %% Refinement 7: Batch Tools
    L3 --> L4[Batch Document Operations]
    L4 --> L5[batch_search_documents<br/>batch_analyze_dependencies]

    %% Refinement 8: DataLoader + 9: Timeout
    L5 --> L6[DataLoader + Timeout<br/>• Eliminate N+1 queries<br/>• 3s SLA guarantee<br/>• Automatic batching]
    L6 --> L7[GraphQL with ACL Filters]

    %% Schedule Team with Refinements
    M --> M1[Schedule Supervisor<br/>+ Scratchpad Access]
    M1 --> M2[Route to Specialist]
    M2 --> M3[Schedule Tools with:<br/>• ACL Filtering<br/>• Timeout Management<br/>• DataLoader Pattern]
    M3 --> M4[Batch Schedule Operations]
    M4 --> M5[batch_get_schedules<br/>batch_compare_tasks]
    M5 --> M6[DataLoader + Timeout<br/>• Eliminate N+1 queries<br/>• 3s SLA guarantee<br/>• Concurrent execution]
    M6 --> M7[GraphQL with ACL Filters]

    %% Cross-Module Team
    N --> N1[Cross-Module Supervisor<br/>+ Scratchpad Access]
    N1 --> N2[Impact Analysis with:<br/>• Shared scratchpad data<br/>• Cross-team coordination<br/>• Batch operations]
    N2 --> N3[Combined GraphQL Queries<br/>Documents + Schedule]

    %% Refinement 12: Self-Healing Retry
    O --> O1[Self-Healing Retry Node]
    O1 --> O2[Error Classification:<br/>Recoverable vs Non-recoverable]
    O2 --> O3[Adaptive Retry Strategies:<br/>• Simplify query<br/>• Add context<br/>• Break down request<br/>• Rephrase question]
    O3 --> O4[Exponential Backoff<br/>+ Jitter]
    O4 --> O5{Max Retries?}
    O5 -->|No| H2
    O5 -->|Yes| P[Fallback Response]

    %% Error Handling
    I --> I1[Generate Clarification<br/>Based on low confidence]
    P --> P1[Graceful Fallback<br/>+ System Guidance]

    %% Response Processing
    L7 --> Q[Response Generation]
    M7 --> Q
    N3 --> Q
    I1 --> Q
    P1 --> Q

    %% Refinement 2: Cache Storage
    Q --> R[Store in Semantic Cache<br/>sentence-transformers + FAISS]
    R --> S[Update Shared Scratchpad<br/>Cross-module findings]
    S --> T[Redis Checkpointer<br/>Save conversation state]
    T --> U[Return Response to User]

    %% Storage & Memory Systems
    subgraph "Storage Layer"
        V[Redis Memory Store<br/>• Conversation history<br/>• Semantic cache<br/>• Shared scratchpad<br/>• Session state]
        W[FAISS Vector Index<br/>• Query embeddings<br/>• Similarity search<br/>• Cache retrieval]
        X[Full History Storage<br/>• Complete conversations<br/>• Context retrieval<br/>• Message optimization]
    end

    %% Backend Integration
    Y[ICMS GraphQL Backend<br/>Nest.js + MySQL] -.->|Filtered Data| L7
    Y -.->|Filtered Data| M7
    Y -.->|Filtered Data| N3

    %% Memory Connections
    V -.->|Load Context| H1
    V -.->|Load Scratchpad| L1
    V -.->|Load Scratchpad| M1
    V -.->|Load Scratchpad| N1
    X -.->|Load full_history| H1
    W -.->|Similarity Search| D2
    T -.->|Save State| V
    R -.->|Store Embeddings| W
    C2 -.->|Store Messages| X
    S -.->|Update Scratchpad| V

    %% Refinement 5: Pydantic Models
    subgraph "Entity Models"
        Z1[Project Entity<br/>Pydantic Validation]
        Z2[Task Entity<br/>Pydantic Validation]
        Z3[Document Entity<br/>Pydantic Validation]
    end

    L7 -.->|Validate| Z1
    L7 -.->|Validate| Z3
    M7 -.->|Validate| Z1
    M7 -.->|Validate| Z2
    N3 -.->|Validate| Z1
    N3 -.->|Validate| Z2
    N3 -.->|Validate| Z3

    %% Performance Monitoring
    subgraph "Monitoring"
        AA[Health Monitor<br/>• Success rates<br/>• SLA compliance<br/>• Error tracking]
        BB[Timeout Manager<br/>• 3s SLA<br/>• Performance metrics<br/>• Retry statistics]
        CC[Cache Statistics<br/>• Hit rates<br/>• Performance gains<br/>• Storage usage]
    end

    L6 -.->|Monitor| AA
    L6 -.->|Track| BB
    M6 -.->|Monitor| AA
    M6 -.->|Track| BB
    D2 -.->|Stats| CC
    R -.->|Stats| CC

    %% Styling
    classDef entry fill:#e3f2fd,stroke:#1565c0,stroke-width:3px
    classDef cache fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef security fill:#ffebee,stroke:#d32f2f,stroke-width:2px
    classDef supervisor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef agent fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef tool fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef storage fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef backend fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef error fill:#fff8e1,stroke:#ff8f00,stroke-width:2px
    classDef refinement fill:#f9fbe7,stroke:#689f38,stroke-width:3px
    classDef entity fill:#e8eaf6,stroke:#3f51b5,stroke-width:2px
    classDef monitor fill:#fafafa,stroke:#616161,stroke-width:2px

    class A,B entry
    class C,C1,C2,C3,D,D1,D2,R,W,CC cache
    class F,F1,F2,F3 security
    class H,L1,M1,N1,J,J1,J2,J3 supervisor
    class L2,L3,M2,M3,N2 agent
    class L4,L5,L6,L7,M4,M5,M6,M7,N3 tool
    class V,X,T storage
    class Y backend
    class I,I1,O,O1,O2,O3,O4,O5,P,P1 error
    class G,G1,G2,H2,H3,H4,S refinement
    class Z1,Z2,Z3 entity
    class AA,BB monitor
