# Schedule Tools Update - Project Schedule Support

This document describes the updates made to the ICMS GraphQL tools to support multiple project schedules per project, with integer ID requirements.

## Overview

The schedule tools have been updated to handle the new data model where:

- Each project can have multiple project schedules
- Both `projectId` and `projectScheduleId` must be integers
- Frontend provides both IDs to the backend
- <PERSON><PERSON> now properly distinguish between listing all schedules vs. getting a specific schedule

## Updated Tools

### 1. `get_project_schedules(project_id: int)`

**Purpose**: Get all project schedules for a specific project

**Parameters**:

- `project_id` (int): The project ID
- `jwt` (str, optional): JWT token for authentication
- `user_permissions` (dict, optional): User permissions for ACL filtering

**Returns**: List of all project schedules for the project

**GraphQL Query**:

```graphql
query GetProjectSchedules($filters: ScheduleFilters) {
  projectSchedules(filters: $filters) {
    id
    name
    description
    startDate
    endDate
    status
    isActive
    createdAt
    updatedAt
    project {
      id
      name
    }
  }
}
```

### 2. `get_project_schedule(project_id: int, project_schedule_id: int)`

**Purpose**: Get detailed information for a specific project schedule

**Parameters**:

- `project_id` (int): The project ID
- `project_schedule_id` (int): The specific project schedule ID
- `jwt` (str, optional): JWT token for authentication
- `user_permissions` (dict, optional): User permissions for ACL filtering

**Returns**: Detailed project schedule with tasks and milestones

**GraphQL Query**:

```graphql
query GetProjectSchedule($projectId: Int!, $projectScheduleId: Int!) {
  projectSchedule(projectId: $projectId, id: $projectScheduleId) {
    id
    name
    description
    startDate
    endDate
    status
    isActive
    project {
      id
      name
    }
    tasks {
      id
      name
      status
      baselineStart
      baselineFinish
      actualStart
      actualFinish
      duration
      progress
      assignees {
        id
        name
        role
        email
      }
      dependencies {
        id
        type
        dependentTaskId
      }
    }
    milestones {
      id
      name
      date
      status
      description
      isBaseline
    }
  }
}
```

### 3. `get_critical_path(project_id: int, project_schedule_id: int)`

**Purpose**: Get critical path analysis for a specific project schedule

**Parameters**:

- `project_id` (int): The project ID
- `project_schedule_id` (int): The specific project schedule ID
- `jwt` (str, optional): JWT token for authentication

**Returns**: Critical path analysis with task dependencies

### 4. `analyze_resource_allocation(project_id: int, project_schedule_id: int, start_date?, end_date?)`

**Purpose**: Analyze resource allocation for a specific project schedule

**Parameters**:

- `project_id` (int): The project ID
- `project_schedule_id` (int): The specific project schedule ID
- `start_date` (str, optional): Start date for analysis (ISO format)
- `end_date` (str, optional): End date for analysis (ISO format)
- `jwt` (str, optional): JWT token for authentication

**Returns**: Resource allocation analysis with conflicts and utilization

### 5. `batch_get_schedules(project_ids: List[int])`

**Purpose**: Batch retrieve all schedules for multiple projects

**Parameters**:

- `project_ids` (List[int]): List of project IDs (integers)
- `jwt` (str, optional): JWT token for authentication
- `user_permissions` (dict, optional): User permissions for ACL filtering

**Returns**: Dictionary with schedule data for each project

## Usage Examples

### Get all schedules for a project

```python
schedules = await get_project_schedules(project_id=123)
print(f"Found {len(schedules['schedules'])} schedules")
```

### Get specific schedule details

```python
schedule = await get_project_schedule(
    project_id=123,
    project_schedule_id=456
)
print(f"Schedule: {schedule['schedule']['name']}")
print(f"Tasks: {len(schedule['schedule']['tasks'])}")
```

### Get critical path for a schedule

```python
critical_path = await get_critical_path(
    project_id=123,
    project_schedule_id=456
)
print(f"Critical path length: {critical_path['criticalPath']['criticalPathLength']}")
```

### Analyze resource allocation

```python
allocation = await analyze_resource_allocation(
    project_id=123,
    project_schedule_id=456,
    start_date="2025-01-01",
    end_date="2025-12-31"
)
print(f"Resources: {len(allocation['resourceAllocation']['resources'])}")
print(f"Conflicts: {len(allocation['resourceAllocation']['conflicts'])}")
```

### Batch get schedules for multiple projects

```python
batch_result = await batch_get_schedules(project_ids=[123, 124, 125])
for project_id, result in batch_result['batch_results'].items():
    if result.get('error'):
        print(f"Project {project_id}: Error - {result['error']}")
    else:
        print(f"Project {project_id}: {len(result['schedules'])} schedules")
```

## Migration Notes

### Breaking Changes

1. **Integer IDs**: All project and schedule IDs must now be integers, not strings
2. **Required Parameters**: Most schedule functions now require both `project_id` and `project_schedule_id`
3. **Return Structure**: `get_project_schedules` returns a list, while `get_project_schedule` returns detailed data

### Frontend Integration

- Frontend must provide both `project_id` and `project_schedule_id` as integers
- Use `get_project_schedules(project_id)` to populate schedule selection dropdowns
- Pass selected `project_schedule_id` to specific schedule functions

### Backward Compatibility

- The old `get_project_schedule(project_id)` function has been replaced
- Use `get_project_schedules(project_id)` to list all schedules for a project
- Use `get_project_schedule(project_id, project_schedule_id)` for specific schedule details

## Error Handling

All tools include proper error handling and will return:

```python
{
    "error": "Error message",
    "schedules": [],  # or appropriate empty structure
}
```

## ACL Filtering

All tools support ACL (Access Control List) filtering through the `user_permissions` parameter to ensure users only see data they have access to.
