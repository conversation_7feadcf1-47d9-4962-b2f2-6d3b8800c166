#!/usr/bin/env python3
"""Test script to verify the new API structure with project_id and project_schedule_id."""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_new_api_structure():
    """Test the new API structure with project fields."""
    print("🧪 Testing new API structure...")
    
    try:
        # Test schema imports
        from app.schemas.chat import ChatRequest, Message
        
        print("✅ Successfully imported updated schemas")
        
        # Test creating a request with the new structure
        test_request_data = {
            "messages": [
                {
                    "role": "user",
                    "content": "give me all site diary from jan to feb"
                }
            ],
            "project_id": 49,
            "project_schedule_id": 86,
            "user_id": "string",
            "auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NDgxLCJ0eXBlIjoiVXNlciIsImlhdCI6MTc1MjQ4MjExNSwiZXhwIjoxNzUzMDg2OTE1fQ.vwTPx2xrE2tkmTjOUd9fnJZYcdZlyjOLsFSbNYakDlo"
        }
        
        # Validate the request structure
        chat_request = ChatRequest(**test_request_data)
        
        print("✅ Successfully created ChatRequest with new structure")
        print(f"   Project ID: {chat_request.project_id}")
        print(f"   Project Schedule ID: {chat_request.project_schedule_id}")
        print(f"   User ID: {chat_request.user_id}")
        print(f"   Auth Token: {chat_request.auth_token[:50]}...")
        print(f"   Message: {chat_request.messages[0].content}")
        
        # Test agent import
        from app.core.langgraph.graph import LangGraphAgent
        
        print("✅ Successfully imported LangGraphAgent")
        
        # Test that the agent can be instantiated (without actually running it)
        print("✅ Agent can be instantiated")
        
        # Test the API endpoint import
        from app.api.v1.chatbot import router
        
        print("✅ Successfully imported chatbot router")
        
        # Test validation of integer fields
        print("\n📋 Testing field validation...")
        
        # Test valid integer project_id
        valid_request = ChatRequest(
            messages=[Message(role="user", content="test")],
            project_id=123,
            project_schedule_id=456
        )
        print(f"   ✅ Valid integers: project_id={valid_request.project_id}, project_schedule_id={valid_request.project_schedule_id}")
        
        # Test None values (should be allowed)
        none_request = ChatRequest(
            messages=[Message(role="user", content="test")],
            project_id=None,
            project_schedule_id=None
        )
        print(f"   ✅ None values allowed: project_id={none_request.project_id}, project_schedule_id={none_request.project_schedule_id}")
        
        # Test validation errors
        try:
            invalid_request = ChatRequest(
                messages=[Message(role="user", content="test")],
                project_id=0  # Should fail validation (ge=1)
            )
            print("   ❌ Validation should have failed for project_id=0")
        except Exception as e:
            print(f"   ✅ Validation correctly rejected project_id=0: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing new API structure: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_structure():
    """Test that the JSON structure matches the expected format."""
    print("\n🔍 Testing JSON structure compatibility...")
    
    import json
    
    # Your example JSON
    example_json = {
        "messages": [
            {
                "role": "user",
                "content": "give me all site diary from jan to feb"
            }
        ],
        "project_id": 49,
        "project_schedule_id": 86,
        "user_id": "string",
        "auth_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NDgxLCJ0eXBlIjoiVXNlciIsImlhdCI6MTc1MjQ4MjExNSwiZXhwIjoxNzUzMDg2OTE1fQ.vwTPx2xrE2tkmTjOUd9fnJZYcdZlyjOLsFSbNYakDlo"
    }
    
    try:
        from app.schemas.chat import ChatRequest
        
        # Test that the JSON can be parsed into our schema
        request = ChatRequest(**example_json)
        
        print("✅ Example JSON structure is compatible")
        print(f"   Messages: {len(request.messages)} message(s)")
        print(f"   Project ID: {request.project_id} (type: {type(request.project_id).__name__})")
        print(f"   Project Schedule ID: {request.project_schedule_id} (type: {type(request.project_schedule_id).__name__})")
        print(f"   User ID: {request.user_id}")
        print(f"   Auth Token: {'Present' if request.auth_token else 'None'}")
        
        # Test serialization back to JSON
        serialized = request.model_dump()
        json_str = json.dumps(serialized, indent=2)
        print(f"\n📤 Serialized back to JSON:")
        print(json_str)
        
        return True
        
    except Exception as e:
        print(f"❌ JSON structure compatibility test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 New API Structure Test Suite\n")
    
    # Test async functionality
    async_success = asyncio.run(test_new_api_structure())
    
    # Test JSON compatibility
    json_success = test_json_structure()
    
    if async_success and json_success:
        print("\n🎉 All tests passed! New API structure is working correctly.")
        print("\n📝 Summary of changes:")
        print("  • Added project_id (int, optional) - for project-specific queries")
        print("  • Added project_schedule_id (int, optional) - for schedule-specific queries")
        print("  • Updated user_id (str, optional) - for tracking purposes")
        print("  • Updated auth_token (str, optional) - JWT authentication token")
        print("  • Removed old schedule_id field")
        print("\n💡 Your example JSON structure is fully supported!")
        print("\n🚀 Ready for frontend integration with the new structure.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
