#!/bin/bash

# Development startup script that disables telemetry before starting the application

echo "🔧 Disabling telemetry for development..."

# Set environment variables to disable OpenTelemetry
export OTEL_SDK_DISABLED=true
export OTEL_EXPORTER_OTLP_ENDPOINT=""
export OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=""
export OTEL_EXPORTER_OTLP_METRICS_ENDPOINT=""
export OTEL_EXPORTER_OTLP_LOGS_ENDPOINT=""
export OTEL_TRACES_EXPORTER=none
export OTEL_METRICS_EXPORTER=none
export OTEL_LOGS_EXPORTER=none
export OTEL_PYTHON_DISABLED_INSTRUMENTATIONS=all

# Disable Langfuse if no credentials are provided
if [ -z "$LANGFUSE_PUBLIC_KEY" ]; then
    export LANGFUSE_PUBLIC_KEY=""
fi

if [ -z "$LANGFUSE_SECRET_KEY" ]; then
    export LANGFUSE_SECRET_KEY=""
fi

echo "✅ Telemetry disabled"
echo "🚀 Starting ICMS Multi-Agent System..."

# Start the application
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
