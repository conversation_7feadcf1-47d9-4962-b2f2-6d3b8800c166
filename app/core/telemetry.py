"""Telemetry configuration for the application.

This module handles OpenTelemetry and Langfuse configuration to prevent
telemetry export errors when credentials are not provided.
"""

import os
from typing import Optional

from langfuse import <PERSON><PERSON>

from app.core.config import settings
from app.core.logging import logger


def setup_telemetry() -> Optional[Langfuse]:
    """Set up telemetry configuration.

    Configures OpenTelemetry and Langfuse based on environment variables.
    Disables telemetry exports when credentials are not provided to prevent errors.

    Returns:
        Optional[Langfuse]: Langfuse client if credentials are provided, None otherwise.
    """
    # Always disable OpenTelemetry in development to prevent connection errors
    disable_telemetry_exports()

    # Initialize Langfuse only if credentials are provided
    if settings.LANGFUSE_PUBLIC_KEY and settings.LANGFUSE_SECRET_KEY:
        try:
            # Create Langfuse client with telemetry disabled
            langfuse_client = Langfuse(
                public_key=settings.LANGFUSE_PUBLIC_KEY,
                secret_key=settings.LANGFUSE_SECRET_KEY,
                host=settings.LANGFUSE_HOST,
                # Disable OpenTelemetry integration in Langfuse
                enabled=True,
                debug=False,
            )
            logger.info("langfuse_initialized", host=settings.LANGFUSE_HOST)
            return langfuse_client
        except Exception as e:
            logger.warning("langfuse_initialization_failed", error=str(e))
            return None
    else:
        logger.info("langfuse_disabled", reason="missing_credentials")
        return None


def disable_telemetry_exports():
    """Disable all telemetry exports to prevent connection errors.
    
    This function sets environment variables to disable OpenTelemetry
    exports when running in development or when telemetry services
    are not available.
    """
    # Disable OpenTelemetry SDK completely
    os.environ["OTEL_SDK_DISABLED"] = "true"
    
    # Disable OTLP exporters
    os.environ["OTEL_EXPORTER_OTLP_ENDPOINT"] = ""
    os.environ["OTEL_EXPORTER_OTLP_TRACES_ENDPOINT"] = ""
    os.environ["OTEL_EXPORTER_OTLP_METRICS_ENDPOINT"] = ""
    os.environ["OTEL_EXPORTER_OTLP_LOGS_ENDPOINT"] = ""
    
    # Disable other common telemetry exporters
    os.environ["OTEL_TRACES_EXPORTER"] = "none"
    os.environ["OTEL_METRICS_EXPORTER"] = "none"
    os.environ["OTEL_LOGS_EXPORTER"] = "none"
    
    logger.info("telemetry_exports_disabled", reason="development_mode")
