"""Telemetry patch to completely disable OpenTelemetry exports.

This module must be imported before any other modules that might
initialize OpenTelemetry to prevent connection errors.
"""

import os
import sys
from typing import Any


def patch_opentelemetry():
    """Patch OpenTelemetry to prevent any exports."""
    # Set environment variables to disable OpenTelemetry
    os.environ.update({
        "OTEL_SDK_DISABLED": "true",
        "OTEL_EXPORTER_OTLP_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_METRICS_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_LOGS_ENDPOINT": "",
        "OTEL_TRACES_EXPORTER": "none",
        "OTEL_METRICS_EXPORTER": "none",
        "OTEL_LOGS_EXPORTER": "none",
        "OTEL_PYTHON_DISABLED_INSTRUMENTATIONS": "all",
    })
    
    # Patch the OpenTelemetry modules to prevent exports
    try:
        # Mock the OTLP exporters to do nothing
        import opentelemetry.exporter.otlp.proto.http.trace_exporter as otlp_http
        import opentelemetry.exporter.otlp.proto.grpc.trace_exporter as otlp_grpc
        
        class NoOpExporter:
            def export(self, *args, **kwargs):
                return None
            
            def shutdown(self, *args, **kwargs):
                return None
        
        # Replace the exporters with no-op versions
        otlp_http.OTLPSpanExporter = NoOpExporter
        otlp_grpc.OTLPSpanExporter = NoOpExporter
        
    except ImportError:
        # OpenTelemetry modules not yet imported, that's fine
        pass


def patch_langfuse():
    """Patch Langfuse to disable its OpenTelemetry integration."""
    try:
        # Import and patch Langfuse's OpenTelemetry integration
        import langfuse.opentelemetry as langfuse_otel
        
        # Disable the OpenTelemetry integration
        def disabled_init(*args, **kwargs):
            pass
        
        # Replace the initialization functions
        if hasattr(langfuse_otel, 'LangfuseSpanProcessor'):
            langfuse_otel.LangfuseSpanProcessor.__init__ = disabled_init
        
    except ImportError:
        # Langfuse not yet imported, that's fine
        pass


def apply_telemetry_patches():
    """Apply all telemetry patches to prevent connection errors."""
    patch_opentelemetry()
    patch_langfuse()


# Apply patches immediately when this module is imported
apply_telemetry_patches()
