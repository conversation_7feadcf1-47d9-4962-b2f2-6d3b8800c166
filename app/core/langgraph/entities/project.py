"""Project entity models for ICMS Multi-Agent System."""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class ProjectStatus(str, Enum):
    """Project status enumeration."""
    PLANNING = "planning"
    ACTIVE = "active"
    ON_HOLD = "on_hold"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Project(BaseModel):
    """Project entity with validation and IDE support."""
    
    id: str = Field(..., description="Unique project identifier")
    name: str = Field(..., min_length=1, max_length=200, description="Project name")
    description: Optional[str] = Field(None, max_length=1000, description="Project description")
    status: ProjectStatus = Field(..., description="Current project status")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, ge=0, description="Project budget")
    manager_id: Optional[str] = Field(None, description="Project manager user ID")
    department: Optional[str] = Field(None, description="Responsible department")
    location: Optional[str] = Field(None, description="Project location")
    tags: List[str] = Field(default_factory=list, description="Project tags")
    
    @validator('end_date')
    def end_date_after_start_date(cls, v, values):
        """Validate that end date is after start date."""
        if v and values.get('start_date') and v < values['start_date']:
            raise ValueError('End date must be after start date')
        return v
    
    @validator('name')
    def name_not_empty(cls, v):
        """Validate that name is not empty or whitespace."""
        if not v or not v.strip():
            raise ValueError('Project name cannot be empty')
        return v.strip()
    
    @property
    def is_active(self) -> bool:
        """Check if project is currently active."""
        return self.status == ProjectStatus.ACTIVE
    
    @property
    def is_completed(self) -> bool:
        """Check if project is completed."""
        return self.status == ProjectStatus.COMPLETED
    
    @property
    def duration_days(self) -> Optional[int]:
        """Calculate project duration in days."""
        if self.start_date and self.end_date:
            return (self.end_date - self.start_date).days
        return None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for GraphQL queries."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status.value,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "budget": self.budget,
            "manager_id": self.manager_id,
            "department": self.department,
            "location": self.location,
            "tags": self.tags
        }
    
    @classmethod
    def from_graphql(cls, data: dict) -> "Project":
        """Create Project from GraphQL response."""
        return cls(
            id=data["id"],
            name=data["name"],
            description=data.get("description"),
            status=ProjectStatus(data["status"]),
            start_date=datetime.fromisoformat(data["start_date"]) if data.get("start_date") else None,
            end_date=datetime.fromisoformat(data["end_date"]) if data.get("end_date") else None,
            budget=data.get("budget"),
            manager_id=data.get("manager_id"),
            department=data.get("department"),
            location=data.get("location"),
            tags=data.get("tags", [])
        )
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
