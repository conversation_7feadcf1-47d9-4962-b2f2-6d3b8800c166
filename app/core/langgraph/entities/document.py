"""Document entity models for ICMS Multi-Agent System."""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class DocumentType(str, Enum):
    """Document type enumeration."""
    DRAWING = "drawing"
    SPECIFICATION = "specification"
    REPORT = "report"
    CONTRACT = "contract"
    PERMIT = "permit"
    PHOTO = "photo"
    VIDEO = "video"
    MANUAL = "manual"
    CERTIFICATE = "certificate"
    OTHER = "other"


class DocumentStatus(str, Enum):
    """Document status enumeration."""
    DRAFT = "draft"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    SUPERSEDED = "superseded"
    ARCHIVED = "archived"


class Document(BaseModel):
    """Document entity with validation and IDE support."""
    
    id: str = Field(..., description="Unique document identifier")
    title: str = Field(..., min_length=1, max_length=300, description="Document title")
    description: Optional[str] = Field(None, max_length=2000, description="Document description")
    type: DocumentType = Field(..., description="Document type")
    status: DocumentStatus = Field(..., description="Document status")
    project_id: str = Field(..., description="Associated project ID")
    
    # Version control
    version: str = Field(default="1.0", description="Document version")
    revision: Optional[str] = Field(None, description="Document revision")
    
    # File information
    file_name: Optional[str] = Field(None, description="Original file name")
    file_size: Optional[int] = Field(None, ge=0, description="File size in bytes")
    file_format: Optional[str] = Field(None, description="File format/extension")
    file_path: Optional[str] = Field(None, description="File storage path")
    
    # Approval workflow
    approval_status: Optional[str] = Field(None, description="Approval workflow status")
    approved_by: Optional[str] = Field(None, description="Approver user ID")
    approved_at: Optional[datetime] = Field(None, description="Approval timestamp")
    
    # Access control
    department: Optional[str] = Field(None, description="Responsible department")
    confidentiality: str = Field(default="public", description="Confidentiality level")
    access_level: str = Field(default="read", description="Default access level")
    
    # Metadata
    author_id: Optional[str] = Field(None, description="Document author user ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    tags: List[str] = Field(default_factory=list, description="Document tags")
    
    # Relationships
    related_document_ids: List[str] = Field(default_factory=list, description="Related document IDs")
    related_task_ids: List[str] = Field(default_factory=list, description="Related task IDs")
    
    @validator('title')
    def title_not_empty(cls, v):
        """Validate that title is not empty or whitespace."""
        if not v or not v.strip():
            raise ValueError('Document title cannot be empty')
        return v.strip()
    
    @validator('version')
    def version_format(cls, v):
        """Validate version format."""
        if not v or not v.strip():
            raise ValueError('Version cannot be empty')
        return v.strip()
    
    @validator('confidentiality')
    def valid_confidentiality(cls, v):
        """Validate confidentiality level."""
        valid_levels = ["public", "internal", "confidential", "restricted"]
        if v not in valid_levels:
            raise ValueError(f'Confidentiality must be one of: {valid_levels}')
        return v
    
    @property
    def is_approved(self) -> bool:
        """Check if document is approved."""
        return self.status == DocumentStatus.APPROVED
    
    @property
    def is_draft(self) -> bool:
        """Check if document is in draft status."""
        return self.status == DocumentStatus.DRAFT
    
    @property
    def is_confidential(self) -> bool:
        """Check if document is confidential."""
        return self.confidentiality in ["confidential", "restricted"]
    
    @property
    def file_size_mb(self) -> Optional[float]:
        """Get file size in megabytes."""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for GraphQL queries."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "type": self.type.value,
            "status": self.status.value,
            "project_id": self.project_id,
            "version": self.version,
            "revision": self.revision,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_format": self.file_format,
            "file_path": self.file_path,
            "approval_status": self.approval_status,
            "approved_by": self.approved_by,
            "approved_at": self.approved_at.isoformat() if self.approved_at else None,
            "department": self.department,
            "confidentiality": self.confidentiality,
            "access_level": self.access_level,
            "author_id": self.author_id,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "tags": self.tags,
            "related_document_ids": self.related_document_ids,
            "related_task_ids": self.related_task_ids
        }
    
    @classmethod
    def from_graphql(cls, data: dict) -> "Document":
        """Create Document from GraphQL response."""
        return cls(
            id=data["id"],
            title=data["title"],
            description=data.get("description"),
            type=DocumentType(data["type"]),
            status=DocumentStatus(data["status"]),
            project_id=data["project_id"],
            version=data.get("version", "1.0"),
            revision=data.get("revision"),
            file_name=data.get("file_name"),
            file_size=data.get("file_size"),
            file_format=data.get("file_format"),
            file_path=data.get("file_path"),
            approval_status=data.get("approval_status"),
            approved_by=data.get("approved_by"),
            approved_at=datetime.fromisoformat(data["approved_at"]) if data.get("approved_at") else None,
            department=data.get("department"),
            confidentiality=data.get("confidentiality", "public"),
            access_level=data.get("access_level", "read"),
            author_id=data.get("author_id"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            tags=data.get("tags", []),
            related_document_ids=data.get("related_document_ids", []),
            related_task_ids=data.get("related_task_ids", [])
        )
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
