"""Task entity models for ICMS Multi-Agent System."""

from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field, validator


class TaskStatus(str, Enum):
    """Task status enumeration."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ON_HOLD = "on_hold"
    CANCELLED = "cancelled"
    OVERDUE = "overdue"


class TaskPriority(str, Enum):
    """Task priority enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class Task(BaseModel):
    """Task entity with validation and IDE support."""
    
    id: str = Field(..., description="Unique task identifier")
    name: str = Field(..., min_length=1, max_length=200, description="Task name")
    description: Optional[str] = Field(None, max_length=2000, description="Task description")
    status: TaskStatus = Field(..., description="Current task status")
    priority: TaskPriority = Field(default=TaskPriority.MEDIUM, description="Task priority")
    project_id: str = Field(..., description="Associated project ID")
    
    # Scheduling
    baseline_start: Optional[datetime] = Field(None, description="Planned start date")
    baseline_finish: Optional[datetime] = Field(None, description="Planned finish date")
    actual_start: Optional[datetime] = Field(None, description="Actual start date")
    actual_finish: Optional[datetime] = Field(None, description="Actual finish date")
    duration: Optional[int] = Field(None, ge=0, description="Duration in days")
    progress: float = Field(default=0.0, ge=0.0, le=100.0, description="Progress percentage")
    
    # Assignment
    assignees: List[str] = Field(default_factory=list, description="Assigned user IDs")
    department: Optional[str] = Field(None, description="Responsible department")
    
    # Dependencies
    predecessor_ids: List[str] = Field(default_factory=list, description="Predecessor task IDs")
    successor_ids: List[str] = Field(default_factory=list, description="Successor task IDs")
    
    # Metadata
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    tags: List[str] = Field(default_factory=list, description="Task tags")
    
    @validator('baseline_finish')
    def baseline_finish_after_start(cls, v, values):
        """Validate that baseline finish is after baseline start."""
        if v and values.get('baseline_start') and v < values['baseline_start']:
            raise ValueError('Baseline finish must be after baseline start')
        return v
    
    @validator('actual_finish')
    def actual_finish_after_start(cls, v, values):
        """Validate that actual finish is after actual start."""
        if v and values.get('actual_start') and v < values['actual_start']:
            raise ValueError('Actual finish must be after actual start')
        return v
    
    @validator('name')
    def name_not_empty(cls, v):
        """Validate that name is not empty or whitespace."""
        if not v or not v.strip():
            raise ValueError('Task name cannot be empty')
        return v.strip()
    
    @property
    def is_overdue(self) -> bool:
        """Check if task is overdue."""
        if self.baseline_finish and self.status not in [TaskStatus.COMPLETED, TaskStatus.CANCELLED]:
            return datetime.now() > self.baseline_finish
        return False
    
    @property
    def is_completed(self) -> bool:
        """Check if task is completed."""
        return self.status == TaskStatus.COMPLETED
    
    @property
    def is_critical(self) -> bool:
        """Check if task has critical priority."""
        return self.priority == TaskPriority.CRITICAL
    
    @property
    def planned_duration_days(self) -> Optional[int]:
        """Calculate planned duration in days."""
        if self.baseline_start and self.baseline_finish:
            return (self.baseline_finish - self.baseline_start).days
        return self.duration
    
    @property
    def actual_duration_days(self) -> Optional[int]:
        """Calculate actual duration in days."""
        if self.actual_start and self.actual_finish:
            return (self.actual_finish - self.actual_start).days
        return None
    
    def to_dict(self) -> dict:
        """Convert to dictionary for GraphQL queries."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "status": self.status.value,
            "priority": self.priority.value,
            "project_id": self.project_id,
            "baseline_start": self.baseline_start.isoformat() if self.baseline_start else None,
            "baseline_finish": self.baseline_finish.isoformat() if self.baseline_finish else None,
            "actual_start": self.actual_start.isoformat() if self.actual_start else None,
            "actual_finish": self.actual_finish.isoformat() if self.actual_finish else None,
            "duration": self.duration,
            "progress": self.progress,
            "assignees": self.assignees,
            "department": self.department,
            "predecessor_ids": self.predecessor_ids,
            "successor_ids": self.successor_ids,
            "tags": self.tags
        }
    
    @classmethod
    def from_graphql(cls, data: dict) -> "Task":
        """Create Task from GraphQL response."""
        return cls(
            id=data["id"],
            name=data["name"],
            description=data.get("description"),
            status=TaskStatus(data["status"]),
            priority=TaskPriority(data.get("priority", "medium")),
            project_id=data["project_id"],
            baseline_start=datetime.fromisoformat(data["baseline_start"]) if data.get("baseline_start") else None,
            baseline_finish=datetime.fromisoformat(data["baseline_finish"]) if data.get("baseline_finish") else None,
            actual_start=datetime.fromisoformat(data["actual_start"]) if data.get("actual_start") else None,
            actual_finish=datetime.fromisoformat(data["actual_finish"]) if data.get("actual_finish") else None,
            duration=data.get("duration"),
            progress=data.get("progress", 0.0),
            assignees=data.get("assignees", []),
            department=data.get("department"),
            predecessor_ids=data.get("predecessor_ids", []),
            successor_ids=data.get("successor_ids", []),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else None,
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else None,
            tags=data.get("tags", [])
        )
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True
        validate_assignment = True
        extra = "forbid"
