"""Batch query tools for ICMS Multi-Agent System.

This module implements batch query tools for 3-5x faster performance
on comparison and multi-item queries.
"""

import asyncio
from typing import Dict, List, Optional

from langchain_core.tools import tool

from app.core.langgraph.tools.icms_graphql_tools import execute_graphql_query
from app.core.logging import logger


@tool
async def batch_search_documents(
    queries: List[Dict], 
    jwt: str = "", 
    user_permissions: dict = None
) -> dict:
    """Batch search for multiple document queries simultaneously.
    
    Args:
        queries: List of search queries, each with project_id, search_term, filters
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering
        
    Returns:
        Dictionary with results for each query
    """
    logger.info("batch_document_search_started", query_count=len(queries))
    
    # Prepare batch GraphQL query
    batch_query = """
    query BatchSearchDocuments($queries: [DocumentSearchInput!]!) {
        batchSearchDocuments(queries: $queries) {
            queryIndex
            documents {
                id
                title
                type
                status
                version
                createdAt
                updatedAt
                approvalStatus
                project {
                    id
                    name
                }
            }
            error
        }
    }
    """
    
    # Apply ACL filters to each query
    if user_permissions:
        from app.core.langgraph.nodes.acl_filter import acl_filter
        for i, query in enumerate(queries):
            base_filters = query.get("filters", {})
            queries[i]["filters"] = acl_filter.apply_acl_filters(
                base_filters, user_permissions, "documents"
            )
    
    variables = {"queries": queries}
    
    try:
        result = await execute_graphql_query(batch_query, variables, jwt)
        
        # Process batch results
        batch_results = {}
        for item in result.get("batchSearchDocuments", []):
            query_index = item["queryIndex"]
            batch_results[f"query_{query_index}"] = {
                "documents": item.get("documents", []),
                "error": item.get("error")
            }
        
        logger.info(
            "batch_document_search_completed",
            query_count=len(queries),
            result_count=len(batch_results)
        )
        
        return {"batch_results": batch_results}
        
    except Exception as e:
        logger.error("batch_document_search_failed", error=str(e))
        return {"error": str(e), "batch_results": {}}


@tool
async def batch_get_schedules(
    project_ids: List[int],
    jwt: str = "",
    user_permissions: dict = None
) -> dict:
    """Batch retrieve schedules for multiple projects simultaneously.

    Args:
        project_ids: List of project IDs to get schedules for (integers)
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering
        
    Returns:
        Dictionary with schedule data for each project
    """
    logger.info("batch_schedule_fetch_started", project_count=len(project_ids))
    
    # Create concurrent tasks for each project
    tasks = []
    for project_id in project_ids:
        task = _get_project_schedules(project_id, jwt, user_permissions)
        tasks.append(task)
    
    try:
        # Execute all queries concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        batch_results = {}
        for i, result in enumerate(results):
            project_id = project_ids[i]
            if isinstance(result, Exception):
                batch_results[project_id] = {"error": str(result), "schedules": []}
            else:
                batch_results[project_id] = result
        
        logger.info(
            "batch_schedule_fetch_completed",
            project_count=len(project_ids),
            success_count=sum(1 for r in batch_results.values() if not r.get("error"))
        )
        
        return {"batch_results": batch_results}
        
    except Exception as e:
        logger.error("batch_schedule_fetch_failed", error=str(e))
        return {"error": str(e), "batch_results": {}}


async def _get_project_schedules(
    project_id: int,
    jwt: str,
    user_permissions: dict
) -> dict:
    """Get all schedules for a single project (internal helper)."""
    query = """
    query GetProjectSchedules($filters: ScheduleFilters) {
        projectSchedules(filters: $filters) {
            id
            name
            description
            startDate
            endDate
            status
            isActive
            createdAt
            updatedAt
            project {
                id
                name
            }
        }
    }
    """

    # Apply ACL filters
    base_filters = {"projectId": {"eq": project_id}}
    if user_permissions:
        from app.core.langgraph.nodes.acl_filter import acl_filter
        filters = acl_filter.apply_acl_filters(base_filters, user_permissions, "schedule")
    else:
        filters = base_filters

    variables = {"filters": filters}

    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"schedules": result.get("projectSchedules", []), "error": None}
    except Exception as e:
        return {"schedules": [], "error": str(e)}


@tool
async def batch_compare_tasks(
    task_ids: List[str], 
    comparison_fields: List[str] = None,
    jwt: str = "", 
    user_permissions: dict = None
) -> dict:
    """Batch compare multiple tasks across specified fields.
    
    Args:
        task_ids: List of task IDs to compare
        comparison_fields: Fields to compare (progress, status, dates, etc.)
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering
        
    Returns:
        Dictionary with comparison results
    """
    if not comparison_fields:
        comparison_fields = ["progress", "status", "baselineStart", "baselineFinish"]
    
    logger.info(
        "batch_task_comparison_started",
        task_count=len(task_ids),
        fields=comparison_fields
    )
    
    # Batch query for task comparison
    query = """
    query BatchCompareTasks($taskIds: [ID!]!, $fields: [String!]!) {
        batchCompareTasks(taskIds: $taskIds, fields: $fields) {
            taskId
            taskName
            comparisonData
            metrics {
                field
                value
                variance
                status
            }
        }
    }
    """
    
    variables = {
        "taskIds": task_ids,
        "fields": comparison_fields
    }
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        
        comparison_results = result.get("batchCompareTasks", [])
        
        # Process comparison data
        processed_results = {
            "tasks": comparison_results,
            "summary": _generate_comparison_summary(comparison_results, comparison_fields)
        }
        
        logger.info(
            "batch_task_comparison_completed",
            task_count=len(task_ids),
            result_count=len(comparison_results)
        )
        
        return {"comparison": processed_results}
        
    except Exception as e:
        logger.error("batch_task_comparison_failed", error=str(e))
        return {"error": str(e), "comparison": None}


def _generate_comparison_summary(results: List[dict], fields: List[str]) -> dict:
    """Generate summary statistics for task comparison."""
    summary = {
        "total_tasks": len(results),
        "field_analysis": {}
    }
    
    for field in fields:
        field_values = []
        for task in results:
            for metric in task.get("metrics", []):
                if metric["field"] == field:
                    field_values.append(metric.get("variance", 0))
        
        if field_values:
            summary["field_analysis"][field] = {
                "avg_variance": sum(field_values) / len(field_values),
                "max_variance": max(field_values),
                "min_variance": min(field_values)
            }
    
    return summary


@tool
async def batch_analyze_dependencies(
    entity_ids: List[str], 
    entity_type: str = "task",
    jwt: str = "", 
    user_permissions: dict = None
) -> dict:
    """Batch analyze dependencies for multiple entities (tasks/documents).
    
    Args:
        entity_ids: List of entity IDs to analyze
        entity_type: Type of entity (task, document)
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering
        
    Returns:
        Dictionary with dependency analysis results
    """
    logger.info(
        "batch_dependency_analysis_started",
        entity_count=len(entity_ids),
        entity_type=entity_type
    )
    
    # Batch dependency analysis query
    query = """
    query BatchAnalyzeDependencies($entityIds: [ID!]!, $entityType: String!) {
        batchAnalyzeDependencies(entityIds: $entityIds, entityType: $entityType) {
            entityId
            entityName
            dependencies {
                id
                name
                type
                relationship
                impact
            }
            dependents {
                id
                name
                type
                relationship
                impact
            }
            criticalPath
            riskLevel
        }
    }
    """
    
    variables = {
        "entityIds": entity_ids,
        "entityType": entity_type
    }
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        
        dependency_results = result.get("batchAnalyzeDependencies", [])
        
        # Process dependency data
        processed_results = {
            "entities": dependency_results,
            "network_analysis": _analyze_dependency_network(dependency_results)
        }
        
        logger.info(
            "batch_dependency_analysis_completed",
            entity_count=len(entity_ids),
            result_count=len(dependency_results)
        )
        
        return {"dependencies": processed_results}
        
    except Exception as e:
        logger.error("batch_dependency_analysis_failed", error=str(e))
        return {"error": str(e), "dependencies": None}


def _analyze_dependency_network(results: List[dict]) -> dict:
    """Analyze the dependency network for insights."""
    network_stats = {
        "total_entities": len(results),
        "critical_path_entities": 0,
        "high_risk_entities": 0,
        "dependency_density": 0
    }
    
    total_dependencies = 0
    for entity in results:
        if entity.get("criticalPath"):
            network_stats["critical_path_entities"] += 1
        if entity.get("riskLevel") == "high":
            network_stats["high_risk_entities"] += 1
        
        total_dependencies += len(entity.get("dependencies", []))
    
    if len(results) > 0:
        network_stats["dependency_density"] = total_dependencies / len(results)
    
    return network_stats
