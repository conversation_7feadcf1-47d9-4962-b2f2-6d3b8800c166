"""LangGraph tools for enhanced language model capabilities.

This package contains custom tools that can be used with LangGraph to extend
the capabilities of language models. Includes ICMS GraphQL tools and web search.
"""

from langchain_core.tools.base import BaseTool

from .duckduckgo_search import duckduckgo_search_tool
from .icms_graphql_tools import (
    analyze_document_relationships,
    analyze_resource_allocation,
    get_critical_path,
    get_project_schedule,
    search_documents,
)
from .batch_tools import (
    batch_search_documents,
    batch_get_schedules,
    batch_compare_tasks,
    batch_analyze_dependencies,
)

# ICMS tools for document and schedule operations
tools: list[BaseTool] = [
    # Document tools
    search_documents,
    analyze_document_relationships,

    # Schedule tools
    get_project_schedule,
    get_critical_path,
    analyze_resource_allocation,

    # Batch tools for performance
    batch_search_documents,
    batch_get_schedules,
    batch_compare_tasks,
    batch_analyze_dependencies,

    # General tools
    duckduckgo_search_tool,
]
