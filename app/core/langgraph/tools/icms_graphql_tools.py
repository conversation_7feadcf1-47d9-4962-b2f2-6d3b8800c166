"""ICMS GraphQL tools for document and schedule operations.

This module implements GraphQL tools for the ICMS multi-agent system using
the tool decorator pattern as specified in the development blueprint.
"""

import asyncio
from typing import Any, Dict, Optional

import httpx
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.logging import logger
from app.core.langgraph.nodes.acl_filter import acl_filter


class GraphQLError(Exception):
    """Exception raised for GraphQL errors."""
    pass


async def execute_graphql_query(
    query: str,
    variables: Optional[Dict[str, Any]] = None,
    auth_token: Optional[str] = None
) -> Dict[str, Any]:
    """Execute a GraphQL query against the ICMS backend.
    
    Args:
        query: GraphQL query string
        variables: Query variables
        auth_token: JWT token for authentication
        
    Returns:
        GraphQL response data
        
    Raises:
        GraphQLError: If the query fails or returns errors
    """
    endpoint = getattr(settings, 'GRAPHQL_ENDPOINT', 'http://localhost:3000/graphql')
    headers = {"Content-Type": "application/json"}
    
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"
    
    payload = {"query": query}
    if variables:
        payload["variables"] = variables
    
    logger.info(
        "graphql_query_executing",
        endpoint=endpoint,
        has_auth=bool(auth_token),
        variables_count=len(variables) if variables else 0
    )
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(endpoint, json=payload, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            
            if "errors" in result:
                error_messages = [error.get("message", str(error)) for error in result["errors"]]
                logger.error("graphql_query_errors", errors=error_messages)
                raise GraphQLError(f"GraphQL errors: {', '.join(error_messages)}")
            
            logger.info("graphql_query_success", data_keys=list(result.get("data", {}).keys()))
            return result.get("data", {})
            
    except httpx.HTTPError as e:
        logger.error("graphql_http_error", error=str(e), endpoint=endpoint)
        raise GraphQLError(f"HTTP error: {e}")
    except Exception as e:
        logger.error("graphql_unexpected_error", error=str(e))
        raise GraphQLError(f"Unexpected error: {e}")


@tool
async def search_documents(project_id: str, search_term: str = "", jwt: str = "", user_permissions: dict = None) -> dict:
    """Search for documents in the ICMS system with ACL filtering.

    Args:
        project_id: ID of the project to search documents in
        search_term: Search term to filter documents
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering

    Returns:
        Dictionary containing document search results
    """
    # Apply ACL filters
    base_filters = {"projectId": {"eq": project_id}} if project_id else {}
    if user_permissions:
        acl_filters = acl_filter.apply_acl_filters(base_filters, user_permissions, "documents")
    else:
        acl_filters = base_filters

    query = """
    query SearchDocuments($filters: DocumentFilters, $searchTerm: String) {
        documents(filters: $filters, searchTerm: $searchTerm) {
            id
            title
            type
            status
            version
            createdAt
            updatedAt
            approvalStatus
            metadata {
                size
                format
                tags
            }
            project {
                id
                name
            }
        }
    }
    """

    variables = {
        "filters": acl_filters,
        "searchTerm": search_term
    }
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"documents": result.get("documents", [])}
    except GraphQLError as e:
        logger.error("document_search_failed", error=str(e))
        return {"error": str(e), "documents": []}


@tool
async def get_project_schedules(project_id: int, jwt: str = "", user_permissions: dict = None) -> dict:
    """Get all project schedules for a project from ICMS with ACL filtering.

    Args:
        project_id: ID of the project to get schedules for (integer)
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering

    Returns:
        Dictionary containing list of project schedules
    """
    # Apply ACL filters
    base_filters = {"projectId": {"eq": project_id}} if project_id else {}
    if user_permissions:
        acl_filters = acl_filter.apply_acl_filters(base_filters, user_permissions, "schedule")
    else:
        acl_filters = base_filters

    query = """
    query GetProjectSchedules($filters: ScheduleFilters) {
        projectSchedules(filters: $filters) {
            id
            name
            description
            startDate
            endDate
            status
            isActive
            createdAt
            updatedAt
            project {
                id
                name
            }
        }
    }
    """

    variables = {"filters": acl_filters}

    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"schedules": result.get("projectSchedules", [])}
    except GraphQLError as e:
        logger.error("project_schedules_fetch_failed", error=str(e))
        return {"error": str(e), "schedules": []}


@tool
async def get_project_schedule(project_id: int, project_schedule_id: int, jwt: str = "", user_permissions: dict = None) -> dict:
    """Get specific project schedule information from ICMS with ACL filtering.

    Args:
        project_id: ID of the project (integer)
        project_schedule_id: ID of the specific project schedule (integer)
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering

    Returns:
        Dictionary containing project schedule data with tasks and milestones
    """
    # Apply ACL filters
    base_filters = {
        "projectId": {"eq": project_id},
        "id": {"eq": project_schedule_id}
    }
    if user_permissions:
        acl_filters = acl_filter.apply_acl_filters(base_filters, user_permissions, "schedule")
    else:
        acl_filters = base_filters

    query = """
    query GetProjectSchedule($projectId: Int!, $projectScheduleId: Int!) {
        projectSchedule(projectId: $projectId, id: $projectScheduleId) {
            id
            name
            description
            startDate
            endDate
            status
            isActive
            createdAt
            updatedAt
            project {
                id
                name
            }
            tasks {
                id
                name
                description
                status
                baselineStart
                baselineFinish
                actualStart
                actualFinish
                duration
                progress
                priority
                assignees {
                    id
                    name
                    role
                    email
                }
                dependencies {
                    id
                    type
                    dependentTaskId
                }
            }
            milestones {
                id
                name
                date
                status
                description
                isBaseline
            }
        }
    }
    """

    variables = {
        "projectId": project_id,
        "projectScheduleId": project_schedule_id
    }

    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"schedule": result.get("projectSchedule")}
    except GraphQLError as e:
        logger.error("schedule_fetch_failed", error=str(e), project_id=project_id, project_schedule_id=project_schedule_id)
        return {"error": str(e), "schedule": None}


@tool
async def get_critical_path(project_id: int, project_schedule_id: int, jwt: str = "") -> dict:
    """Get critical path analysis for a specific project schedule.

    Args:
        project_id: ID of the project to analyze (integer)
        project_schedule_id: ID of the specific project schedule (integer)
        jwt: JWT token for authentication

    Returns:
        Dictionary containing critical path analysis
    """
    query = """
    query GetCriticalPath($projectId: Int!, $projectScheduleId: Int!) {
        criticalPath(projectId: $projectId, projectScheduleId: $projectScheduleId) {
            tasks {
                id
                name
                baselineStart
                baselineFinish
                actualStart
                actualFinish
                duration
                float
                isCritical
                predecessors {
                    id
                    name
                    relationshipType
                }
                successors {
                    id
                    name
                    relationshipType
                }
            }
            totalDuration
            criticalPathLength
            projectSchedule {
                id
                name
                startDate
                endDate
            }
        }
    }
    """

    variables = {
        "projectId": project_id,
        "projectScheduleId": project_schedule_id
    }

    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"criticalPath": result.get("criticalPath")}
    except GraphQLError as e:
        logger.error("critical_path_analysis_failed", error=str(e), project_id=project_id, project_schedule_id=project_schedule_id)
        return {"error": str(e), "criticalPath": None}


@tool
async def analyze_document_relationships(document_id: str, jwt: str = "") -> dict:
    """Analyze relationships and dependencies for a document.
    
    Args:
        document_id: ID of the document to analyze
        jwt: JWT token for authentication
        
    Returns:
        Dictionary containing document relationship analysis
    """
    query = """
    query AnalyzeDocumentRelationships($documentId: ID!) {
        documentRelationships(documentId: $documentId) {
            document {
                id
                title
                type
            }
            relatedDocuments {
                id
                title
                type
                relationshipType
            }
            affectedTasks {
                id
                name
                status
                impact
            }
            dependencies {
                type
                description
                status
            }
        }
    }
    """
    
    variables = {"documentId": document_id}
    
    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"relationships": result.get("documentRelationships")}
    except GraphQLError as e:
        logger.error("document_relationship_analysis_failed", error=str(e))
        return {"error": str(e), "relationships": None}


@tool
async def analyze_resource_allocation(project_id: int, project_schedule_id: int, start_date: str = "", end_date: str = "", jwt: str = "") -> dict:
    """Analyze resource allocation for a specific project schedule within a date range.

    Args:
        project_id: ID of the project to analyze (integer)
        project_schedule_id: ID of the specific project schedule (integer)
        start_date: Start date for analysis (ISO format)
        end_date: End date for analysis (ISO format)
        jwt: JWT token for authentication

    Returns:
        Dictionary containing resource allocation analysis
    """
    query = """
    query AnalyzeResourceAllocation($projectId: Int!, $projectScheduleId: Int!, $dateRange: DateRange) {
        resourceAllocation(projectId: $projectId, projectScheduleId: $projectScheduleId, dateRange: $dateRange) {
            projectSchedule {
                id
                name
                startDate
                endDate
            }
            resources {
                id
                name
                type
                capacity
                utilization
                allocation {
                    taskId
                    taskName
                    allocatedHours
                    startDate
                    endDate
                    percentage
                }
            }
            conflicts {
                resourceId
                resourceName
                overallocation
                conflictingTasks {
                    id
                    name
                    startDate
                    endDate
                    allocatedHours
                }
            }
            summary {
                totalResources
                overallocatedResources
                underutilizedResources
                averageUtilization
            }
        }
    }
    """

    variables = {
        "projectId": project_id,
        "projectScheduleId": project_schedule_id,
        "dateRange": {
            "start": start_date,
            "end": end_date
        } if start_date and end_date else None
    }

    try:
        result = await execute_graphql_query(query, variables, jwt)
        return {"resourceAllocation": result.get("resourceAllocation")}
    except GraphQLError as e:
        logger.error("resource_allocation_analysis_failed", error=str(e), project_id=project_id, project_schedule_id=project_schedule_id)
        return {"error": str(e), "resourceAllocation": None}
