"""Schedule Team Sub-Graph for ICMS Multi-Agent System.

This module implements the schedule team sub-graph with specialized agents
for planning, critical path analysis, and resource management using ToolNode pattern.
"""

from typing import Dict, List, Literal, Optional

from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import ToolNode

from app.core.langgraph.tools.icms_graphql_tools import (
    analyze_resource_allocation,
    get_critical_path,
    get_project_schedule,
)
from app.core.langgraph.utils import (
    analyze_chat_history,
    format_context_summary,
    should_use_previous_context,
)
from app.core.logging import logger
from app.schemas import IcmsState


def schedule_supervisor_node(state: IcmsState) -> IcmsState:
    """Schedule team supervisor node for routing schedule-related queries.

    Args:
        state: Current ICMS state

    Returns:
        Updated state with routing decision
    """
    logger.info("schedule_supervisor_analyzing_query")

    # Analyze chat history for context (-10 messages)
    conversation_context = analyze_chat_history(state["messages"], limit=10)
    context_summary = format_context_summary(conversation_context)

    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    if not user_messages:
        return {**state, "requires_clarify": True}

    query = user_messages[-1].content.lower()
    entities = state.get("entities", {})
    use_context = should_use_previous_context(conversation_context, query)

    logger.info(
        "schedule_supervisor_with_context",
        query_preview=query[:100],
        context_summary=context_summary,
        use_context=use_context
    )
    
    # Determine which schedule tools to use based on query analysis
    tools_to_use = []
    
    # Check for general schedule keywords
    schedule_keywords = ["schedule", "tasks", "timeline", "project", "activities", "overview"]
    if any(keyword in query for keyword in schedule_keywords):
        tools_to_use.append("get_project_schedule")
    
    # Check for critical path keywords
    critical_keywords = ["critical", "path", "critical path", "bottleneck", "delay", "longest"]
    if any(keyword in query for keyword in critical_keywords):
        tools_to_use.append("get_critical_path")
    
    # Check for resource keywords
    resource_keywords = ["resource", "allocation", "capacity", "workload", "assignment", "conflict"]
    if any(keyword in query for keyword in resource_keywords):
        tools_to_use.append("analyze_resource_allocation")
    
    # Default to schedule overview if no specific tools identified
    if not tools_to_use:
        tools_to_use = ["get_project_schedule"]

    logger.info(
        "schedule_supervisor_routing",
        tools_selected=tools_to_use,
        query_preview=query[:100]
    )

    # Store routing decision in state
    return {
        **state,
        "schedule_tools_to_use": tools_to_use
    }


def planning_agent_node(state: IcmsState) -> IcmsState:
    """Planning agent specialist node for schedule overview and planning.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with planning results
    """
    logger.info("planning_agent_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")

    # Extract project ID and project schedule ID from entities
    project_id = entities.get("project_id")
    project_schedule_id = entities.get("project_schedule_id")

    if not project_id or not project_schedule_id:
        # Return error if required IDs are missing
        error_message = AIMessage(
            content="Error: Both project_id and project_schedule_id are required for schedule queries. Please provide both values."
        )
        return {**state, "messages": state["messages"] + [error_message]}

    # Create a tool node for schedule planning
    planning_tool_node = ToolNode([get_project_schedule])

    # Prepare tool call message
    tool_call_message = AIMessage(
        content="Retrieving project schedule and planning information...",
        tool_calls=[{
            "name": "get_project_schedule",
            "args": {
                "project_id": project_id,
                "project_schedule_id": project_schedule_id,
                "jwt": user_jwt
            },
            "id": "get_schedule_call"
        }]
    )
    
    # Execute the tool
    tool_state = {**state, "messages": state["messages"] + [tool_call_message]}
    result_state = planning_tool_node.invoke(tool_state)
    
    logger.info("planning_agent_completed")
    return result_state


def critical_path_agent_node(state: IcmsState) -> IcmsState:
    """Critical path agent specialist node for critical path analysis.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with critical path analysis
    """
    logger.info("critical_path_agent_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")

    # Extract project ID and project schedule ID from entities
    project_id = entities.get("project_id")
    project_schedule_id = entities.get("project_schedule_id")

    if not project_id or not project_schedule_id:
        # Return error if required IDs are missing
        error_message = AIMessage(
            content="Error: Both project_id and project_schedule_id are required for critical path analysis. Please provide both values."
        )
        return {**state, "messages": state["messages"] + [error_message]}

    # Create a tool node for critical path analysis
    critical_path_tool_node = ToolNode([get_critical_path])

    # Prepare tool call message
    tool_call_message = AIMessage(
        content="Analyzing critical path and identifying bottlenecks...",
        tool_calls=[{
            "name": "get_critical_path",
            "args": {
                "project_id": project_id,
                "project_schedule_id": project_schedule_id,
                "jwt": user_jwt
            },
            "id": "get_critical_path_call"
        }]
    )
    
    # Execute the tool
    tool_state = {**state, "messages": state["messages"] + [tool_call_message]}
    result_state = critical_path_tool_node.invoke(tool_state)
    
    logger.info("critical_path_agent_completed")
    return result_state


def resource_agent_node(state: IcmsState) -> IcmsState:
    """Resource agent specialist node for resource allocation analysis.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with resource analysis
    """
    logger.info("resource_agent_executing")
    
    entities = state.get("entities", {})
    user_jwt = state.get("user_jwt", "")

    # Extract project ID and project schedule ID from entities
    project_id = entities.get("project_id")
    project_schedule_id = entities.get("project_schedule_id")
    start_date = entities.get("start_date", "")
    end_date = entities.get("end_date", "")

    if not project_id or not project_schedule_id:
        # Return error if required IDs are missing
        error_message = AIMessage(
            content="Error: Both project_id and project_schedule_id are required for resource analysis. Please provide both values."
        )
        return {**state, "messages": state["messages"] + [error_message]}

    # Create a tool node for resource analysis
    resource_tool_node = ToolNode([analyze_resource_allocation])

    # Prepare tool call message
    tool_call_message = AIMessage(
        content="Analyzing resource allocation and identifying conflicts...",
        tool_calls=[{
            "name": "analyze_resource_allocation",
            "args": {
                "project_id": project_id,
                "project_schedule_id": project_schedule_id,
                "start_date": start_date,
                "end_date": end_date,
                "jwt": user_jwt
            },
            "id": "analyze_resources_call"
        }]
    )
    
    # Execute the tool
    tool_state = {**state, "messages": state["messages"] + [tool_call_message]}
    result_state = resource_tool_node.invoke(tool_state)
    
    logger.info("resource_agent_completed")
    return result_state


def schedule_routing_condition(state: IcmsState) -> Literal["planning", "critical_path", "resource", "end"]:
    """Determine which schedule specialist to route to.

    Args:
        state: Current ICMS state

    Returns:
        Next node to execute
    """
    tools_to_use = state.get("schedule_tools_to_use", [])

    if "get_project_schedule" in tools_to_use:
        return "planning"
    elif "get_critical_path" in tools_to_use:
        return "critical_path"
    elif "analyze_resource_allocation" in tools_to_use:
        return "resource"
    else:
        return "planning"  # Default to planning


def create_schedule_team_subgraph() -> StateGraph:
    """Create the schedule team sub-graph.
    
    Returns:
        Compiled schedule team sub-graph
    """
    logger.info("creating_schedule_team_subgraph")
    
    # Create the sub-graph
    subgraph = StateGraph(IcmsState)
    
    # Add nodes
    subgraph.add_node("supervisor", schedule_supervisor_node)
    subgraph.add_node("planning", planning_agent_node)
    subgraph.add_node("critical_path", critical_path_agent_node)
    subgraph.add_node("resource", resource_agent_node)

    # Add edges
    subgraph.add_conditional_edges(
        "supervisor",
        schedule_routing_condition,
        {
            "planning": "planning",
            "critical_path": "critical_path",
            "resource": "resource",
            "end": END
        }
    )

    # All specialist nodes end the sub-graph
    subgraph.add_edge("planning", END)
    subgraph.add_edge("critical_path", END)
    subgraph.add_edge("resource", END)
    
    # Set entry point
    subgraph.set_entry_point("supervisor")
    
    logger.info("schedule_team_subgraph_created")
    return subgraph.compile()
