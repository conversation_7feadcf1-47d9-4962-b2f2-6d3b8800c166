"""DataLoader pattern implementation for ICMS GraphQL queries.

This module implements the DataLoader pattern to eliminate N+1 queries
and provide async caching for GraphQL operations.
"""

import asyncio
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, TypeVar

from app.core.logging import logger

T = TypeVar('T')


class DataLoader:
    """DataLoader implementation for batching and caching GraphQL queries."""
    
    def __init__(
        self, 
        batch_load_fn: Callable[[List[str]], List[Any]], 
        cache: bool = True,
        max_batch_size: int = 100,
        batch_timeout_ms: int = 10
    ):
        """Initialize DataLoader.
        
        Args:
            batch_load_fn: Function that takes a list of keys and returns a list of values
            cache: Whether to cache results
            max_batch_size: Maximum number of keys to batch together
            batch_timeout_ms: Maximum time to wait before executing batch
        """
        self.batch_load_fn = batch_load_fn
        self.cache_enabled = cache
        self.max_batch_size = max_batch_size
        self.batch_timeout_ms = batch_timeout_ms
        
        # Internal state
        self._cache: Dict[str, Any] = {}
        self._batch_queue: List[str] = []
        self._batch_futures: Dict[str, asyncio.Future] = {}
        self._batch_timer: Optional[asyncio.Task] = None
        
        logger.info(
            "dataloader_initialized",
            cache_enabled=cache,
            max_batch_size=max_batch_size,
            batch_timeout_ms=batch_timeout_ms
        )
    
    async def load(self, key: str) -> Any:
        """Load a single value by key.
        
        Args:
            key: The key to load
            
        Returns:
            The loaded value
        """
        # Check cache first
        if self.cache_enabled and key in self._cache:
            logger.debug("dataloader_cache_hit", key=key)
            return self._cache[key]
        
        # Check if already in current batch
        if key in self._batch_futures:
            logger.debug("dataloader_batch_pending", key=key)
            return await self._batch_futures[key]
        
        # Add to batch queue
        future = asyncio.Future()
        self._batch_futures[key] = future
        self._batch_queue.append(key)
        
        logger.debug("dataloader_key_queued", key=key, queue_size=len(self._batch_queue))
        
        # Start batch timer if not already running
        if self._batch_timer is None:
            self._batch_timer = asyncio.create_task(self._batch_timeout())
        
        # Execute batch if max size reached
        if len(self._batch_queue) >= self.max_batch_size:
            await self._execute_batch()
        
        return await future
    
    async def load_many(self, keys: List[str]) -> List[Any]:
        """Load multiple values by keys.
        
        Args:
            keys: List of keys to load
            
        Returns:
            List of loaded values in the same order as keys
        """
        logger.debug("dataloader_load_many", key_count=len(keys))
        
        # Create tasks for all keys
        tasks = [self.load(key) for key in keys]
        
        # Wait for all to complete
        results = await asyncio.gather(*tasks)
        
        logger.debug("dataloader_load_many_completed", key_count=len(keys))
        return results
    
    async def _batch_timeout(self):
        """Handle batch timeout."""
        try:
            await asyncio.sleep(self.batch_timeout_ms / 1000.0)
            if self._batch_queue:
                await self._execute_batch()
        except asyncio.CancelledError:
            pass
    
    async def _execute_batch(self):
        """Execute the current batch."""
        if not self._batch_queue:
            return
        
        # Get current batch
        batch_keys = self._batch_queue.copy()
        batch_futures = {k: self._batch_futures[k] for k in batch_keys}
        
        # Clear batch state
        self._batch_queue.clear()
        self._batch_futures.clear()
        
        # Cancel timer
        if self._batch_timer:
            self._batch_timer.cancel()
            self._batch_timer = None
        
        logger.info("dataloader_executing_batch", batch_size=len(batch_keys))
        
        try:
            # Execute batch load function
            results = await self._safe_batch_load(batch_keys)
            
            # Resolve futures and update cache
            for i, key in enumerate(batch_keys):
                result = results[i] if i < len(results) else None
                
                # Cache result
                if self.cache_enabled and result is not None:
                    self._cache[key] = result
                
                # Resolve future
                if key in batch_futures:
                    batch_futures[key].set_result(result)
            
            logger.info("dataloader_batch_completed", batch_size=len(batch_keys))
            
        except Exception as e:
            logger.error("dataloader_batch_failed", error=str(e), batch_size=len(batch_keys))
            
            # Reject all futures
            for future in batch_futures.values():
                if not future.done():
                    future.set_exception(e)
    
    async def _safe_batch_load(self, keys: List[str]) -> List[Any]:
        """Safely execute batch load function."""
        try:
            if asyncio.iscoroutinefunction(self.batch_load_fn):
                return await self.batch_load_fn(keys)
            else:
                return self.batch_load_fn(keys)
        except Exception as e:
            logger.error("batch_load_function_error", error=str(e))
            raise
    
    def clear_cache(self, key: Optional[str] = None):
        """Clear cache for a specific key or all keys.
        
        Args:
            key: Specific key to clear, or None to clear all
        """
        if key is None:
            self._cache.clear()
            logger.info("dataloader_cache_cleared_all")
        elif key in self._cache:
            del self._cache[key]
            logger.info("dataloader_cache_cleared_key", key=key)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        return {
            "cache_size": len(self._cache),
            "cache_enabled": self.cache_enabled,
            "max_batch_size": self.max_batch_size,
            "batch_timeout_ms": self.batch_timeout_ms,
            "current_queue_size": len(self._batch_queue)
        }


class GraphQLDataLoader:
    """Specialized DataLoader for GraphQL operations."""
    
    def __init__(self, execute_graphql_fn: Callable):
        """Initialize GraphQL DataLoader.
        
        Args:
            execute_graphql_fn: Function to execute GraphQL queries
        """
        self.execute_graphql_fn = execute_graphql_fn
        
        # Create specialized loaders for different entity types
        self.project_loader = DataLoader(self._batch_load_projects)
        self.task_loader = DataLoader(self._batch_load_tasks)
        self.document_loader = DataLoader(self._batch_load_documents)
        
        logger.info("graphql_dataloader_initialized")
    
    async def _batch_load_projects(self, project_ids: List[str]) -> List[Dict]:
        """Batch load projects by IDs."""
        query = """
        query BatchLoadProjects($ids: [ID!]!) {
            projects(ids: $ids) {
                id
                name
                status
                startDate
                endDate
                department
            }
        }
        """
        
        variables = {"ids": project_ids}
        
        try:
            result = await self.execute_graphql_fn(query, variables)
            projects = result.get("projects", [])
            
            # Create a map for quick lookup
            project_map = {p["id"]: p for p in projects}
            
            # Return results in the same order as requested IDs
            return [project_map.get(pid) for pid in project_ids]
            
        except Exception as e:
            logger.error("batch_load_projects_failed", error=str(e))
            return [None] * len(project_ids)
    
    async def _batch_load_tasks(self, task_ids: List[str]) -> List[Dict]:
        """Batch load tasks by IDs."""
        query = """
        query BatchLoadTasks($ids: [ID!]!) {
            tasks(ids: $ids) {
                id
                name
                status
                progress
                baselineStart
                baselineFinish
                projectId
            }
        }
        """
        
        variables = {"ids": task_ids}
        
        try:
            result = await self.execute_graphql_fn(query, variables)
            tasks = result.get("tasks", [])
            
            # Create a map for quick lookup
            task_map = {t["id"]: t for t in tasks}
            
            # Return results in the same order as requested IDs
            return [task_map.get(tid) for tid in task_ids]
            
        except Exception as e:
            logger.error("batch_load_tasks_failed", error=str(e))
            return [None] * len(task_ids)
    
    async def _batch_load_documents(self, document_ids: List[str]) -> List[Dict]:
        """Batch load documents by IDs."""
        query = """
        query BatchLoadDocuments($ids: [ID!]!) {
            documents(ids: $ids) {
                id
                title
                type
                status
                version
                projectId
                approvalStatus
            }
        }
        """
        
        variables = {"ids": document_ids}
        
        try:
            result = await self.execute_graphql_fn(query, variables)
            documents = result.get("documents", [])
            
            # Create a map for quick lookup
            doc_map = {d["id"]: d for d in documents}
            
            # Return results in the same order as requested IDs
            return [doc_map.get(did) for did in document_ids]
            
        except Exception as e:
            logger.error("batch_load_documents_failed", error=str(e))
            return [None] * len(document_ids)
    
    async def load_project(self, project_id: str) -> Optional[Dict]:
        """Load a single project."""
        return await self.project_loader.load(project_id)
    
    async def load_task(self, task_id: str) -> Optional[Dict]:
        """Load a single task."""
        return await self.task_loader.load(task_id)
    
    async def load_document(self, document_id: str) -> Optional[Dict]:
        """Load a single document."""
        return await self.document_loader.load(document_id)
    
    async def load_projects(self, project_ids: List[str]) -> List[Optional[Dict]]:
        """Load multiple projects."""
        return await self.project_loader.load_many(project_ids)
    
    async def load_tasks(self, task_ids: List[str]) -> List[Optional[Dict]]:
        """Load multiple tasks."""
        return await self.task_loader.load_many(task_ids)
    
    async def load_documents(self, document_ids: List[str]) -> List[Optional[Dict]]:
        """Load multiple documents."""
        return await self.document_loader.load_many(document_ids)
    
    def clear_cache(self, entity_type: Optional[str] = None):
        """Clear cache for specific entity type or all.
        
        Args:
            entity_type: 'projects', 'tasks', 'documents', or None for all
        """
        if entity_type == "projects" or entity_type is None:
            self.project_loader.clear_cache()
        if entity_type == "tasks" or entity_type is None:
            self.task_loader.clear_cache()
        if entity_type == "documents" or entity_type is None:
            self.document_loader.clear_cache()
        
        logger.info("graphql_dataloader_cache_cleared", entity_type=entity_type or "all")


# Global GraphQL DataLoader instance
graphql_dataloader: Optional[GraphQLDataLoader] = None


def get_graphql_dataloader() -> GraphQLDataLoader:
    """Get or create the global GraphQL DataLoader instance."""
    global graphql_dataloader
    
    if graphql_dataloader is None:
        from app.core.langgraph.tools.icms_graphql_tools import execute_graphql_query
        graphql_dataloader = GraphQLDataLoader(execute_graphql_query)
    
    return graphql_dataloader
