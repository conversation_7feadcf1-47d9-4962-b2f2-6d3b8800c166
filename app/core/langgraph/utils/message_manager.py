"""Message management utilities for ICMS Multi-Agent System.

This module provides utilities for managing conversation messages with
last_10 slice optimization and full history storage in Redis.
"""

import json
from typing import List, Optional, Tuple, Union

from langchain_core.messages import BaseMessage
import redis.asyncio as redis

from app.core.config import settings
from app.core.logging import logger


class MessageManager:
    """Manages conversation messages with optimized storage and retrieval."""
    
    def __init__(self):
        """Initialize the message manager."""
        self._redis_client: Optional[redis.Redis] = None
    
    async def _get_redis_client(self) -> redis.Redis:
        """Get Redis client for message storage."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
            )
        return self._redis_client
    
    async def store_full_history(self, session_id: str, messages: List[Union[BaseMessage, dict]]) -> str:
        """Store complete conversation history in Redis.
        
        Args:
            session_id: Session identifier
            messages: Complete list of messages
            
        Returns:
            Redis key for the stored history
        """
        redis_client = await self._get_redis_client()
        history_key = f"icms:full_history:{session_id}"
        
        # Serialize messages for storage
        serialized_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                # Already a dictionary from dump_messages
                serialized_messages.append({
                    "type": msg.get("type", "dict"),
                    "content": msg.get("content", ""),
                    "additional_kwargs": msg.get("additional_kwargs", {}),
                    "tool_calls": msg.get("tool_calls", []),
                })
            else:
                # Message object
                serialized_messages.append({
                    "type": msg.__class__.__name__,
                    "content": msg.content,
                    "additional_kwargs": getattr(msg, "additional_kwargs", {}),
                    "tool_calls": getattr(msg, "tool_calls", []),
                })
        
        # Store with TTL
        await redis_client.setex(
            history_key,
            settings.REDIS_TTL,
            json.dumps(serialized_messages)
        )
        
        logger.info(
            "full_history_stored",
            session_id=session_id,
            history_key=history_key,
            message_count=len(messages)
        )
        
        return history_key
    
    async def get_full_history(self, history_key: str) -> List[dict]:
        """Retrieve complete conversation history from Redis.
        
        Args:
            history_key: Redis key for the history
            
        Returns:
            List of serialized messages
        """
        redis_client = await self._get_redis_client()
        
        try:
            history_data = await redis_client.get(history_key)
            if history_data:
                messages = json.loads(history_data)
                logger.info(
                    "full_history_retrieved",
                    history_key=history_key,
                    message_count=len(messages)
                )
                return messages
            else:
                logger.warning("full_history_not_found", history_key=history_key)
                return []
        except Exception as e:
            logger.error("full_history_retrieval_error", error=str(e), history_key=history_key)
            return []
    
    def get_last_10_messages(self, messages: List[Union[BaseMessage, dict]]) -> List[Union[BaseMessage, dict]]:
        """Extract last 10 messages for prompt efficiency.
        
        Args:
            messages: Complete list of messages
            
        Returns:
            Last 10 messages
        """
        last_10 = messages[-10:] if len(messages) > 10 else messages
        
        logger.info(
            "last_10_extracted",
            total_messages=len(messages),
            last_10_count=len(last_10)
        )
        
        return last_10
    
    async def prepare_state_messages(
        self, 
        session_id: str, 
        all_messages: List[Union[BaseMessage, dict]]
    ) -> Tuple[List[Union[BaseMessage, dict]], str]:
        """Prepare messages for state with last_10 optimization.
        
        Args:
            session_id: Session identifier
            all_messages: Complete list of messages
            
        Returns:
            Tuple of (last_10_messages, full_history_id)
        """
        # Store full history
        full_history_id = await self.store_full_history(session_id, all_messages)
        
        # Get last 10 for state
        last_10 = self.get_last_10_messages(all_messages)
        
        logger.info(
            "state_messages_prepared",
            session_id=session_id,
            total_messages=len(all_messages),
            state_messages=len(last_10),
            full_history_id=full_history_id
        )
        
        return last_10, full_history_id
    
    async def get_contextual_messages(
        self, 
        full_history_id: str, 
        context_window: int = 20
    ) -> List[dict]:
        """Get extended context messages for analysis.
        
        Args:
            full_history_id: Redis key for full history
            context_window: Number of messages to retrieve for context
            
        Returns:
            Extended context messages
        """
        full_history = await self.get_full_history(full_history_id)
        
        # Get last N messages for context analysis
        context_messages = full_history[-context_window:] if len(full_history) > context_window else full_history
        
        logger.info(
            "contextual_messages_retrieved",
            full_history_id=full_history_id,
            total_history=len(full_history),
            context_messages=len(context_messages),
            context_window=context_window
        )
        
        return context_messages


# Global message manager instance
message_manager = MessageManager()
