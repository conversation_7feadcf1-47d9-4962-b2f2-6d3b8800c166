"""Timeout wrapper for ICMS tools to ensure predictable SLA.

This module provides timeout wrappers for all tools to prevent hanging
and ensure predictable response times.
"""

import asyncio
import functools
from typing import Any, Callable, Optional

from app.core.logging import logger


class TimeoutError(Exception):
    """Custom timeout error for tool operations."""
    pass


def with_timeout(timeout_seconds: float = 3.0, fallback_result: Any = None):
    """Decorator to add timeout to async functions.
    
    Args:
        timeout_seconds: Timeout in seconds (default: 3.0)
        fallback_result: Result to return on timeout (default: None)
        
    Returns:
        Decorated function with timeout
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            function_name = func.__name__
            
            logger.debug(
                "tool_timeout_started",
                function=function_name,
                timeout_seconds=timeout_seconds
            )
            
            try:
                # Execute function with timeout
                result = await asyncio.wait_for(
                    func(*args, **kwargs),
                    timeout=timeout_seconds
                )
                
                logger.debug(
                    "tool_timeout_completed",
                    function=function_name,
                    timeout_seconds=timeout_seconds
                )
                
                return result
                
            except asyncio.TimeoutError:
                logger.warning(
                    "tool_timeout_exceeded",
                    function=function_name,
                    timeout_seconds=timeout_seconds,
                    fallback_used=fallback_result is not None
                )
                
                # Return fallback result or raise custom timeout error
                if fallback_result is not None:
                    return fallback_result
                else:
                    raise TimeoutError(
                        f"Tool '{function_name}' timed out after {timeout_seconds} seconds"
                    )
                    
            except Exception as e:
                logger.error(
                    "tool_execution_error",
                    function=function_name,
                    error=str(e)
                )
                raise
        
        return wrapper
    return decorator


def with_retry_timeout(
    timeout_seconds: float = 3.0,
    max_retries: int = 2,
    retry_delay: float = 0.5,
    fallback_result: Any = None
):
    """Decorator to add timeout with retry logic.
    
    Args:
        timeout_seconds: Timeout in seconds per attempt
        max_retries: Maximum number of retry attempts
        retry_delay: Delay between retries in seconds
        fallback_result: Result to return on final timeout
        
    Returns:
        Decorated function with timeout and retry
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            function_name = func.__name__
            
            for attempt in range(max_retries + 1):
                try:
                    logger.debug(
                        "tool_retry_timeout_attempt",
                        function=function_name,
                        attempt=attempt + 1,
                        max_attempts=max_retries + 1,
                        timeout_seconds=timeout_seconds
                    )
                    
                    # Execute function with timeout
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=timeout_seconds
                    )
                    
                    logger.debug(
                        "tool_retry_timeout_success",
                        function=function_name,
                        attempt=attempt + 1
                    )
                    
                    return result
                    
                except asyncio.TimeoutError:
                    if attempt < max_retries:
                        logger.warning(
                            "tool_retry_timeout_retry",
                            function=function_name,
                            attempt=attempt + 1,
                            retry_delay=retry_delay
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            "tool_retry_timeout_final_failure",
                            function=function_name,
                            total_attempts=max_retries + 1,
                            timeout_seconds=timeout_seconds
                        )
                        
                        if fallback_result is not None:
                            return fallback_result
                        else:
                            raise TimeoutError(
                                f"Tool '{function_name}' timed out after {max_retries + 1} attempts "
                                f"({timeout_seconds}s each)"
                            )
                            
                except Exception as e:
                    if attempt < max_retries:
                        logger.warning(
                            "tool_retry_error_retry",
                            function=function_name,
                            attempt=attempt + 1,
                            error=str(e),
                            retry_delay=retry_delay
                        )
                        await asyncio.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            "tool_retry_final_error",
                            function=function_name,
                            total_attempts=max_retries + 1,
                            error=str(e)
                        )
                        raise
        
        return wrapper
    return decorator


class ToolTimeoutManager:
    """Manager for tool timeouts and SLA monitoring."""
    
    def __init__(self):
        self.timeout_stats = {}
        self.sla_threshold = 5.0  # 5 seconds SLA
    
    def record_execution(self, tool_name: str, duration: float, success: bool):
        """Record tool execution statistics.
        
        Args:
            tool_name: Name of the tool
            duration: Execution duration in seconds
            success: Whether execution was successful
        """
        if tool_name not in self.timeout_stats:
            self.timeout_stats[tool_name] = {
                "total_calls": 0,
                "successful_calls": 0,
                "timeout_calls": 0,
                "total_duration": 0.0,
                "max_duration": 0.0,
                "min_duration": float('inf'),
                "sla_violations": 0
            }
        
        stats = self.timeout_stats[tool_name]
        stats["total_calls"] += 1
        stats["total_duration"] += duration
        stats["max_duration"] = max(stats["max_duration"], duration)
        stats["min_duration"] = min(stats["min_duration"], duration)
        
        if success:
            stats["successful_calls"] += 1
        else:
            stats["timeout_calls"] += 1
        
        if duration > self.sla_threshold:
            stats["sla_violations"] += 1
        
        logger.info(
            "tool_execution_recorded",
            tool_name=tool_name,
            duration=duration,
            success=success,
            sla_violation=duration > self.sla_threshold
        )
    
    def get_tool_stats(self, tool_name: str) -> Optional[dict]:
        """Get statistics for a specific tool.
        
        Args:
            tool_name: Name of the tool
            
        Returns:
            Dictionary with tool statistics or None if not found
        """
        if tool_name not in self.timeout_stats:
            return None
        
        stats = self.timeout_stats[tool_name].copy()
        
        # Calculate derived metrics
        if stats["total_calls"] > 0:
            stats["success_rate"] = stats["successful_calls"] / stats["total_calls"]
            stats["timeout_rate"] = stats["timeout_calls"] / stats["total_calls"]
            stats["avg_duration"] = stats["total_duration"] / stats["total_calls"]
            stats["sla_compliance"] = 1.0 - (stats["sla_violations"] / stats["total_calls"])
        else:
            stats["success_rate"] = 0.0
            stats["timeout_rate"] = 0.0
            stats["avg_duration"] = 0.0
            stats["sla_compliance"] = 1.0
        
        return stats
    
    def get_all_stats(self) -> dict:
        """Get statistics for all tools.
        
        Returns:
            Dictionary with statistics for all tools
        """
        all_stats = {}
        for tool_name in self.timeout_stats:
            all_stats[tool_name] = self.get_tool_stats(tool_name)
        
        return all_stats
    
    def get_sla_report(self) -> dict:
        """Generate SLA compliance report.
        
        Returns:
            Dictionary with SLA compliance metrics
        """
        total_calls = 0
        total_violations = 0
        tool_compliance = {}
        
        for tool_name, stats in self.timeout_stats.items():
            tool_stats = self.get_tool_stats(tool_name)
            if tool_stats:
                total_calls += tool_stats["total_calls"]
                total_violations += tool_stats["sla_violations"]
                tool_compliance[tool_name] = tool_stats["sla_compliance"]
        
        overall_compliance = 1.0 - (total_violations / total_calls) if total_calls > 0 else 1.0
        
        return {
            "overall_sla_compliance": overall_compliance,
            "total_calls": total_calls,
            "total_violations": total_violations,
            "sla_threshold_seconds": self.sla_threshold,
            "tool_compliance": tool_compliance
        }


# Global timeout manager instance
timeout_manager = ToolTimeoutManager()


# Convenience decorators with common timeout values
graphql_timeout = with_timeout(timeout_seconds=3.0, fallback_result={"error": "GraphQL timeout", "data": None})
search_timeout = with_retry_timeout(timeout_seconds=2.0, max_retries=1, fallback_result={"error": "Search timeout", "results": []})
analysis_timeout = with_timeout(timeout_seconds=5.0, fallback_result={"error": "Analysis timeout", "analysis": None})
