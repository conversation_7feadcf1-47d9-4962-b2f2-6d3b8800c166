"""Semantic cache for ICMS Multi-Agent System.

This module provides semantic caching using sentence-transformers and FAISS
to re-use answers for semantically similar questions.
"""

import hashlib
import json
import pickle
from typing import Dict, List, Optional, Tuple

import faiss
import numpy as np
import redis.asyncio as redis
from sentence_transformers import SentenceTransformer

from app.core.config import settings
from app.core.logging import logger


class SemanticCache:
    """Semantic cache for storing and retrieving similar query responses."""
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", similarity_threshold: float = 0.85):
        """Initialize semantic cache.
        
        Args:
            model_name: Sentence transformer model name
            similarity_threshold: Minimum similarity score for cache hit
        """
        self.model_name = model_name
        self.similarity_threshold = similarity_threshold
        self._model: Optional[SentenceTransformer] = None
        self._redis_client: Optional[redis.Redis] = None
        self._index: Optional[faiss.IndexFlatIP] = None
        self._query_cache: Dict[str, str] = {}  # query_hash -> redis_key mapping
        
        logger.info(
            "semantic_cache_initialized",
            model_name=model_name,
            similarity_threshold=similarity_threshold
        )
    
    async def _get_model(self) -> SentenceTransformer:
        """Get or initialize the sentence transformer model."""
        if self._model is None:
            try:
                self._model = SentenceTransformer(self.model_name)
                logger.info("sentence_transformer_loaded", model_name=self.model_name)
            except Exception as e:
                logger.error("sentence_transformer_load_failed", error=str(e))
                # Fallback to a basic model
                self._model = SentenceTransformer("all-MiniLM-L6-v2")
        return self._model
    
    async def _get_redis_client(self) -> redis.Redis:
        """Get Redis client for cache storage."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=False,  # We'll store binary data
                socket_connect_timeout=5,
                socket_timeout=5,
            )
        return self._redis_client
    
    def _get_index(self) -> faiss.IndexFlatIP:
        """Get or initialize FAISS index for similarity search."""
        if self._index is None:
            # Initialize with 384 dimensions (all-MiniLM-L6-v2 embedding size)
            self._index = faiss.IndexFlatIP(384)
            logger.info("faiss_index_initialized", dimensions=384)
        return self._index
    
    def _normalize_query(self, query: str, intent: str, entities: dict) -> str:
        """Normalize query for consistent caching.
        
        Args:
            query: User query
            intent: Classified intent
            entities: Extracted entities
            
        Returns:
            Normalized query string
        """
        # Create a canonical representation
        normalized = {
            "query": query.lower().strip(),
            "intent": intent,
            "entities": {k: v for k, v in sorted(entities.items()) if k not in ["is_followup", "conversation_topics"]}
        }
        return json.dumps(normalized, sort_keys=True)
    
    def _get_query_hash(self, normalized_query: str) -> str:
        """Generate hash for query."""
        return hashlib.sha256(normalized_query.encode()).hexdigest()[:16]
    
    async def get_cached_response(
        self, 
        query: str, 
        intent: str, 
        entities: dict
    ) -> Optional[dict]:
        """Check if a similar query response exists in cache.
        
        Args:
            query: User query
            intent: Classified intent
            entities: Extracted entities
            
        Returns:
            Cached response if found, None otherwise
        """
        try:
            model = await self._get_model()
            redis_client = await self._get_redis_client()
            index = self._get_index()
            
            # Normalize and encode query
            normalized_query = self._normalize_query(query, intent, entities)
            query_embedding = model.encode([normalized_query])
            
            # Search for similar queries
            if index.ntotal > 0:
                # Normalize embeddings for cosine similarity
                faiss.normalize_L2(query_embedding)
                
                # Search for most similar
                scores, indices = index.search(query_embedding, k=5)
                
                for score, idx in zip(scores[0], indices[0]):
                    if score >= self.similarity_threshold:
                        # Get the cached response
                        cache_key = f"icms:semantic_cache:{idx}"
                        cached_data = await redis_client.get(cache_key)
                        
                        if cached_data:
                            response_data = pickle.loads(cached_data)
                            logger.info(
                                "semantic_cache_hit",
                                similarity_score=float(score),
                                cache_key=cache_key,
                                original_query=response_data.get("original_query", "")[:50]
                            )
                            return response_data["response"]
            
            logger.info("semantic_cache_miss", query_preview=query[:50])
            return None
            
        except Exception as e:
            logger.error("semantic_cache_get_error", error=str(e))
            return None
    
    async def store_response(
        self, 
        query: str, 
        intent: str, 
        entities: dict, 
        response: dict
    ) -> None:
        """Store query response in semantic cache.
        
        Args:
            query: User query
            intent: Classified intent
            entities: Extracted entities
            response: System response to cache
        """
        try:
            model = await self._get_model()
            redis_client = await self._get_redis_client()
            index = self._get_index()
            
            # Normalize and encode query
            normalized_query = self._normalize_query(query, intent, entities)
            query_hash = self._get_query_hash(normalized_query)
            
            # Skip if already cached
            if query_hash in self._query_cache:
                return
            
            query_embedding = model.encode([normalized_query])
            faiss.normalize_L2(query_embedding)
            
            # Add to FAISS index
            current_idx = index.ntotal
            index.add(query_embedding)
            
            # Store in Redis
            cache_key = f"icms:semantic_cache:{current_idx}"
            cache_data = {
                "original_query": query,
                "normalized_query": normalized_query,
                "intent": intent,
                "entities": entities,
                "response": response,
                "timestamp": np.datetime64('now').astype(str)
            }
            
            await redis_client.setex(
                cache_key,
                settings.REDIS_TTL * 2,  # Cache responses longer than conversations
                pickle.dumps(cache_data)
            )
            
            # Update local mapping
            self._query_cache[query_hash] = cache_key
            
            logger.info(
                "semantic_cache_stored",
                cache_key=cache_key,
                index_size=index.ntotal,
                query_preview=query[:50]
            )
            
        except Exception as e:
            logger.error("semantic_cache_store_error", error=str(e))
    
    async def clear_cache(self) -> None:
        """Clear the semantic cache."""
        try:
            redis_client = await self._get_redis_client()
            
            # Clear Redis cache entries
            pattern = "icms:semantic_cache:*"
            keys = await redis_client.keys(pattern)
            if keys:
                await redis_client.delete(*keys)
            
            # Reset FAISS index
            self._index = None
            self._query_cache.clear()
            
            logger.info("semantic_cache_cleared", cleared_keys=len(keys))
            
        except Exception as e:
            logger.error("semantic_cache_clear_error", error=str(e))
    
    async def get_cache_stats(self) -> dict:
        """Get cache statistics.
        
        Returns:
            Dictionary with cache statistics
        """
        try:
            redis_client = await self._get_redis_client()
            index = self._get_index()
            
            # Count cache entries
            pattern = "icms:semantic_cache:*"
            keys = await redis_client.keys(pattern)
            
            stats = {
                "total_cached_queries": len(keys),
                "faiss_index_size": index.ntotal if index else 0,
                "similarity_threshold": self.similarity_threshold,
                "model_name": self.model_name
            }
            
            logger.info("semantic_cache_stats", **stats)
            return stats
            
        except Exception as e:
            logger.error("semantic_cache_stats_error", error=str(e))
            return {"error": str(e)}


# Global semantic cache instance
semantic_cache = SemanticCache()
