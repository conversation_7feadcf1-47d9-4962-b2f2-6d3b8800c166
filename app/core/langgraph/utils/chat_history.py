"""Chat history utilities for ICMS Multi-Agent System.

This module provides utilities for analyzing conversation history to improve
context understanding across all agents.
"""

from typing import Dict, List, Optional, Tuple

from langchain_core.messages import AIMessage, HumanMessage

from app.core.logging import logger


class ConversationContext:
    """Container for conversation context analysis."""
    
    def __init__(self):
        self.mentioned_projects: List[str] = []
        self.mentioned_dates: List[str] = []
        self.mentioned_documents: List[str] = []
        self.mentioned_tasks: List[str] = []
        self.conversation_topics: List[str] = []
        self.last_intent: Optional[str] = None
        self.follow_up_indicators: List[str] = []


def analyze_chat_history(messages: List, limit: int = 10) -> ConversationContext:
    """Analyze the last N messages to extract conversation context.
    
    Args:
        messages: List of conversation messages
        limit: Number of recent messages to analyze (default: 10)
        
    Returns:
        ConversationContext with extracted information
    """
    context = ConversationContext()
    
    # Get the last N messages
    recent_messages = messages[-limit:] if len(messages) > limit else messages
    
    logger.info(
        "analyzing_chat_history",
        total_messages=len(messages),
        analyzed_messages=len(recent_messages),
        limit=limit
    )
    
    # Extract context from messages
    for msg in recent_messages:
        if isinstance(msg, HumanMessage):
            _extract_user_context(msg.content, context)
        elif isinstance(msg, AIMessage):
            _extract_ai_context(msg.content, context)
    
    logger.info(
        "chat_history_analysis_complete",
        projects=len(context.mentioned_projects),
        dates=len(context.mentioned_dates),
        documents=len(context.mentioned_documents),
        tasks=len(context.mentioned_tasks),
        topics=len(context.conversation_topics)
    )
    
    return context


def _extract_user_context(content: str, context: ConversationContext) -> None:
    """Extract context from user messages."""
    content_lower = content.lower()
    
    # Project references
    project_keywords = ["project", "proj", "alpha", "beta", "gamma", "phase"]
    for keyword in project_keywords:
        if keyword in content_lower:
            context.mentioned_projects.append(keyword)
    
    # Date/time references
    date_keywords = [
        "today", "tomorrow", "yesterday", "this week", "next week", "last week",
        "this month", "next month", "last month", "this year", "q1", "q2", "q3", "q4",
        "january", "february", "march", "april", "may", "june",
        "july", "august", "september", "october", "november", "december"
    ]
    for keyword in date_keywords:
        if keyword in content_lower:
            context.mentioned_dates.append(keyword)
    
    # Document references
    document_keywords = [
        "document", "drawing", "specification", "report", "file", "pdf", "dwg",
        "blueprint", "plan", "design", "approval", "permit"
    ]
    for keyword in document_keywords:
        if keyword in content_lower:
            context.mentioned_documents.append(keyword)
    
    # Task references
    task_keywords = [
        "task", "activity", "work", "job", "milestone", "deadline", "schedule",
        "overdue", "pending", "complete", "finished", "started"
    ]
    for keyword in task_keywords:
        if keyword in content_lower:
            context.mentioned_tasks.append(keyword)
    
    # Follow-up indicators
    followup_keywords = [
        "also", "and", "what about", "how about", "for the same", "similar",
        "more details", "tell me more", "expand on", "continue", "next"
    ]
    for keyword in followup_keywords:
        if keyword in content_lower:
            context.follow_up_indicators.append(keyword)


def _extract_ai_context(content: str, context: ConversationContext) -> None:
    """Extract context from AI responses."""
    content_lower = content.lower()
    
    # Determine conversation topics from AI responses
    if "document" in content_lower or "drawing" in content_lower:
        context.conversation_topics.append("documents")
        context.last_intent = "DOC_INTENT"
    
    if "schedule" in content_lower or "task" in content_lower:
        context.conversation_topics.append("schedule")
        context.last_intent = "SCHED_INTENT"
    
    if "impact" in content_lower or "dependency" in content_lower:
        context.conversation_topics.append("cross_module")
        context.last_intent = "CROSS_INTENT"


def get_contextual_entities(context: ConversationContext, current_entities: Dict) -> Dict:
    """Enhance current entities with conversation context.
    
    Args:
        context: Analyzed conversation context
        current_entities: Entities from current query
        
    Returns:
        Enhanced entities dictionary
    """
    enhanced_entities = current_entities.copy()
    
    # Add project context if missing
    if not enhanced_entities.get("project_id") and context.mentioned_projects:
        enhanced_entities["historical_projects"] = context.mentioned_projects
    
    # Add date context if missing
    if not enhanced_entities.get("date_range") and context.mentioned_dates:
        enhanced_entities["historical_dates"] = context.mentioned_dates
    
    # Add document context
    if context.mentioned_documents:
        enhanced_entities["historical_documents"] = context.mentioned_documents
    
    # Add task context
    if context.mentioned_tasks:
        enhanced_entities["historical_tasks"] = context.mentioned_tasks
    
    # Add conversation flow indicators
    enhanced_entities["is_followup"] = len(context.follow_up_indicators) > 0
    enhanced_entities["conversation_topics"] = context.conversation_topics
    enhanced_entities["last_intent"] = context.last_intent
    
    return enhanced_entities


def should_use_previous_context(context: ConversationContext, current_query: str) -> bool:
    """Determine if the current query should use previous conversation context.
    
    Args:
        context: Analyzed conversation context
        current_query: Current user query
        
    Returns:
        True if previous context should be used
    """
    current_lower = current_query.lower()
    
    # Check for follow-up indicators
    followup_patterns = [
        "what about", "how about", "and", "also", "for the same", "similar",
        "more details", "tell me more", "continue", "next", "same project"
    ]
    
    has_followup = any(pattern in current_lower for pattern in followup_patterns)
    
    # Check for vague queries that need context
    vague_patterns = [
        "show me", "what are", "how many", "when", "where", "which ones"
    ]
    
    is_vague = any(pattern in current_lower for pattern in vague_patterns)
    
    # Use context if it's a follow-up or vague query with conversation history
    return (has_followup or is_vague) and len(context.conversation_topics) > 0


def format_context_summary(context: ConversationContext) -> str:
    """Format conversation context into a readable summary.
    
    Args:
        context: Analyzed conversation context
        
    Returns:
        Formatted context summary
    """
    summary_parts = []
    
    if context.mentioned_projects:
        summary_parts.append(f"Projects discussed: {', '.join(set(context.mentioned_projects))}")
    
    if context.mentioned_dates:
        summary_parts.append(f"Time periods mentioned: {', '.join(set(context.mentioned_dates))}")
    
    if context.conversation_topics:
        summary_parts.append(f"Topics covered: {', '.join(set(context.conversation_topics))}")
    
    if context.last_intent:
        summary_parts.append(f"Last intent: {context.last_intent}")
    
    if context.follow_up_indicators:
        summary_parts.append("This appears to be a follow-up question")
    
    return " | ".join(summary_parts) if summary_parts else "No significant context found"
