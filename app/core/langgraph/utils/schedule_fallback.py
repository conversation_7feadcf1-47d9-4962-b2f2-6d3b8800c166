"""Schedule fallback utilities for intelligent project schedule selection.

This module provides utilities for automatically selecting the most appropriate
project schedule when the user doesn't specify a project_schedule_id.
"""

import re
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple

from app.core.logging import logger
from app.core.langgraph.tools.icms_graphql_tools import get_project_schedules


async def resolve_project_schedule_id(
    project_id: int,
    user_query: str,
    entities: Dict,
    jwt: str = "",
    user_permissions: Dict = None
) -> Tuple[Optional[int], Dict]:
    """Resolve project_schedule_id using intelligent fallback when not provided.
    
    Args:
        project_id: The project ID
        user_query: The user's original query
        entities: Extracted entities from the query
        jwt: JWT token for authentication
        user_permissions: User permissions for ACL filtering
        
    Returns:
        Tuple of (project_schedule_id, fallback_info)
        fallback_info contains details about the selection process
    """
    logger.info("schedule_fallback_resolving", project_id=project_id)
    
    # Check if project_schedule_id is already in entities
    if entities.get("project_schedule_id"):
        return entities["project_schedule_id"], {"fallback_used": False}
    
    # Get all available schedules for the project
    schedules_result = await get_project_schedules(project_id, jwt, user_permissions)
    
    if schedules_result.get("error"):
        return None, {
            "fallback_used": True,
            "error": f"Failed to get project schedules: {schedules_result['error']}"
        }
    
    schedules = schedules_result.get("schedules", [])
    
    if not schedules:
        return None, {
            "fallback_used": True,
            "error": f"No schedules found for project {project_id}"
        }
    
    if len(schedules) == 1:
        schedule = schedules[0]
        return schedule["id"], {
            "fallback_used": True,
            "selection_reason": "only_available_schedule",
            "selected_schedule": {
                "id": schedule["id"],
                "name": schedule.get("name"),
                "total_available": 1
            }
        }
    
    # Use intelligent selection for multiple schedules
    selected_schedule = _select_best_schedule_with_query_context(schedules, user_query)
    
    fallback_info = {
        "fallback_used": True,
        "selection_reason": selected_schedule.get("_selection_reason"),
        "selected_schedule": {
            "id": selected_schedule["id"],
            "name": selected_schedule.get("name"),
            "total_available": len(schedules)
        },
        "all_available_schedules": [
            {"id": s["id"], "name": s.get("name"), "isActive": s.get("isActive")}
            for s in schedules
        ]
    }
    
    logger.info("schedule_fallback_completed",
               project_id=project_id,
               selected_schedule_id=selected_schedule["id"],
               selection_reason=selected_schedule.get("_selection_reason"),
               total_schedules=len(schedules))
    
    return selected_schedule["id"], fallback_info


def _select_best_schedule_with_query_context(schedules: List[Dict], user_query: str) -> Dict:
    """Select the best schedule considering both schedule properties and user query context.
    
    Args:
        schedules: List of available project schedules
        user_query: The user's original query for additional context
        
    Returns:
        Selected schedule with added _selection_reason field
    """
    if len(schedules) == 1:
        schedules[0]["_selection_reason"] = "only_available_schedule"
        return schedules[0]
    
    # Create a copy to avoid modifying original data
    candidates = [schedule.copy() for schedule in schedules]
    current_date = datetime.now(timezone.utc)
    
    for schedule in candidates:
        score = 0
        reasons = []
        
        # 1. Query-based scoring (highest priority)
        query_score, query_reasons = _score_schedule_by_query(schedule, user_query)
        score += query_score
        reasons.extend(query_reasons)
        
        # 2. Active status (high priority)
        if schedule.get("isActive"):
            score += 100
            reasons.append("active")
        
        # 3. Current period check
        try:
            start_date = datetime.fromisoformat(schedule.get("startDate", "").replace("Z", "+00:00"))
            end_date = datetime.fromisoformat(schedule.get("endDate", "").replace("Z", "+00:00"))
            
            if start_date <= current_date <= end_date:
                score += 50
                reasons.append("current_period")
            elif start_date > current_date:
                score += 25
                reasons.append("future")
        except (ValueError, TypeError):
            pass
        
        # 4. Name-based priority keywords
        name = schedule.get("name", "").lower()
        priority_keywords = ["baseline", "current", "active", "main", "primary", "approved"]
        if any(keyword in name for keyword in priority_keywords):
            score += 30
            reasons.append("priority_name")
        
        # 5. Status-based scoring
        status = schedule.get("status", "").lower()
        if status in ["approved", "published", "active", "current"]:
            score += 15
            reasons.append("approved_status")
        
        # 6. Recency (more recent = better)
        try:
            created_date = datetime.fromisoformat(schedule.get("createdAt", "").replace("Z", "+00:00"))
            days_old = (current_date - created_date).days
            recency_score = max(0, 20 - (days_old // 30))
            score += recency_score
            if recency_score > 10:
                reasons.append("recent")
        except (ValueError, TypeError):
            pass
        
        schedule["_score"] = score
        schedule["_selection_reason"] = ", ".join(reasons) if reasons else "default"
    
    # Select the highest scoring schedule
    best_schedule = max(candidates, key=lambda s: s["_score"])
    
    logger.info("schedule_selection_scoring",
               total_candidates=len(candidates),
               selected_score=best_schedule["_score"],
               selected_name=best_schedule.get("name"),
               all_scores=[(s.get("name"), s["_score"]) for s in candidates])
    
    return best_schedule


def _score_schedule_by_query(schedule: Dict, user_query: str) -> Tuple[int, List[str]]:
    """Score a schedule based on how well it matches the user's query context.
    
    Args:
        schedule: Schedule to score
        user_query: User's original query
        
    Returns:
        Tuple of (score, reasons)
    """
    score = 0
    reasons = []
    query_lower = user_query.lower()
    name_lower = schedule.get("name", "").lower()
    description_lower = schedule.get("description", "").lower()
    
    # Direct name matching (highest priority for query context)
    if any(word in name_lower for word in query_lower.split() if len(word) > 3):
        score += 80
        reasons.append("name_match")
    
    # Temporal keywords in query
    temporal_keywords = {
        "current": ["current", "now", "today", "present"],
        "baseline": ["baseline", "original", "planned", "initial"],
        "latest": ["latest", "newest", "recent", "updated"],
        "active": ["active", "ongoing", "running"],
        "future": ["future", "upcoming", "next", "planned"]
    }
    
    for category, keywords in temporal_keywords.items():
        if any(keyword in query_lower for keyword in keywords):
            if category in name_lower or category in description_lower:
                score += 60
                reasons.append(f"temporal_match_{category}")
            elif category == "current" and schedule.get("isActive"):
                score += 40
                reasons.append("temporal_active_match")
    
    # Version/revision keywords
    version_patterns = [
        r"v\d+", r"version\s*\d+", r"rev\s*\d+", r"revision\s*\d+"
    ]
    
    query_versions = []
    name_versions = []
    
    for pattern in version_patterns:
        query_versions.extend(re.findall(pattern, query_lower))
        name_versions.extend(re.findall(pattern, name_lower))
    
    if query_versions and name_versions:
        if any(qv in name_versions for qv in query_versions):
            score += 70
            reasons.append("version_match")
    
    return score, reasons
