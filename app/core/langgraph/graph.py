"""This file contains the LangGraph Agent/workflow and interactions with the LLM."""

from typing import (
    Any,
    AsyncGenerator,
    Dict,
    Literal,
    Optional,
)

from asgiref.sync import sync_to_async
from langchain_core.messages import (
    BaseMessage,
    ToolMessage,
    convert_to_openai_messages,
)
from langchain_openai import AzureChatOpenAI
from langfuse.langchain import <PERSON>backHandler
from langgraph.checkpoint.memory import MemorySaver
from langgraph.checkpoint.redis.aio import AsyncRedisSaver
from langgraph.graph import (
    END,
    StateGraph,
)
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import StateSnapshot
from openai import OpenAIError
import redis.asyncio as redis


from app.core.config import (
    Environment,
    settings,
)
from app.core.langgraph.nodes import classify_intent_node, clarification_node, fallback_node, acl_filter_node
from app.core.langgraph.utils import message_manager, semantic_cache
from app.core.langgraph.subgraphs import (
    create_cross_module_subgraph,
    create_document_team_subgraph,
    create_schedule_team_subgraph,
)
from app.core.langgraph.tools import tools
from app.core.logging import logger
from app.core.metrics import llm_inference_duration_seconds
from app.core.prompts import SYSTEM_PROMPT
from app.schemas import (
    GraphState,
    IcmsState,
    Message,
)
from app.utils import (
    dump_messages,
    prepare_messages,
)


class LangGraphAgent:
    """Manages the LangGraph Agent/workflow and interactions with the LLM.

    This class handles the creation and management of the LangGraph workflow,
    including LLM interactions, database connections, and response processing.
    """

    def __init__(self):
        """Initialize the LangGraph Agent with Azure OpenAI."""
        # Map Azure deployment names to standard model names for token counting
        model_name_mapping = {
            "gpt-4o-mini": "gpt-4o-mini",
            "gpt-4o": "gpt-4o", 
            "gpt-4": "gpt-4",
            "gpt-35-turbo": "gpt-3.5-turbo",
            "gpt-3.5-turbo": "gpt-3.5-turbo"
        }
        
        # Get the standard model name for tiktoken
        standard_model_name = model_name_mapping.get(
            settings.AZURE_OPENAI_DEPLOYMENT_NAME, 
            "gpt-4o-mini"  # Default fallback
        )
        
        # Create Azure OpenAI client
        llm = AzureChatOpenAI(
            azure_deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            api_key=settings.AZURE_OPENAI_API_KEY,
            temperature=settings.DEFAULT_LLM_TEMPERATURE,
            max_tokens=settings.MAX_TOKENS,
            model=standard_model_name,  # Use standard model name for token counting
            **self._get_model_kwargs(),
        )
        
        # Ensure model_name is set for token counting before binding tools
        llm.model_name = standard_model_name
        
        # Bind tools to create the final LLM
        self.llm = llm.bind_tools(tools)
        
        self.tools_by_name = {tool.name: tool for tool in tools}
        self._redis_client: Optional[redis.Redis] = None
        self._graph: Optional[CompiledStateGraph] = None

        # Force graph recreation for new architecture
        self.force_graph_recreation()
        logger.info("langgraph_agent_initialized",
                   environment=settings.ENVIRONMENT.value)

        logger.info(
            "azure_openai_initialized", 
            deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
            model_name=self.llm.model_name,
            standard_model_name=standard_model_name,
            endpoint=settings.AZURE_OPENAI_ENDPOINT,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            environment=settings.ENVIRONMENT.value
        )

    def force_graph_recreation(self):
        """Force recreation of the graph to include new workflow."""
        self._graph = None
        logger.info("graph_cache_cleared_for_recreation")

    def _get_model_kwargs(self) -> Dict[str, Any]:
        """Get environment-specific model kwargs.

        Returns:
            Dict[str, Any]: Additional model arguments based on environment
        """
        model_kwargs = {}

        # Development - we can use lower speeds for cost savings
        if settings.ENVIRONMENT == Environment.DEVELOPMENT:
            model_kwargs["top_p"] = 0.8

        # Production - use higher quality settings
        elif settings.ENVIRONMENT == Environment.PRODUCTION:
            model_kwargs["top_p"] = 0.95
            model_kwargs["presence_penalty"] = 0.1
            model_kwargs["frequency_penalty"] = 0.1

        return model_kwargs



    async def _get_redis_client(self) -> redis.Redis:
        """Get a Redis client for checkpointing and session management.

        Returns:
            redis.Redis: A Redis client for the ICMS system.
        """
        if self._redis_client is None:
            try:
                self._redis_client = redis.from_url(
                    settings.REDIS_URL,
                    encoding="utf-8",
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                )
                # Test the connection
                await self._redis_client.ping()
                logger.info("redis_client_created", url=settings.REDIS_URL, environment=settings.ENVIRONMENT.value)
            except Exception as e:
                logger.error("redis_client_creation_failed", error=str(e), environment=settings.ENVIRONMENT.value)
                # In production, we might want to degrade gracefully
                if settings.ENVIRONMENT == Environment.PRODUCTION:
                    logger.warning("continuing_without_redis", environment=settings.ENVIRONMENT.value)
                    return None
                raise e
        return self._redis_client

    async def _chat(self, state: GraphState) -> dict:
        """Process the chat state and generate a response.

        Args:
            state (GraphState): The current state of the conversation.

        Returns:
            dict: Updated state with new messages.
        """
        messages = prepare_messages(state.messages, self.llm, SYSTEM_PROMPT)

        llm_calls_num = 0

        # Configure retry attempts based on environment
        max_retries = settings.MAX_LLM_CALL_RETRIES

        for attempt in range(max_retries):
            try:
                logger.info(
                    "azure_openai_call_starting",
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                    environment=settings.ENVIRONMENT.value,
                )
                with llm_inference_duration_seconds.labels(model=self.llm.model_name).time():
                    generated_state = {"messages": [await self.llm.ainvoke(dump_messages(messages))]}
                logger.info(
                    "azure_openai_response_generated",
                    session_id=state.session_id,
                    llm_calls_num=llm_calls_num + 1,
                    deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                    environment=settings.ENVIRONMENT.value,
                )
                return generated_state
            except OpenAIError as e:
                logger.error(
                    "azure_openai_call_failed",
                    llm_calls_num=llm_calls_num,
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    error=str(e),
                    error_type=type(e).__name__,
                    deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                    endpoint=settings.AZURE_OPENAI_ENDPOINT,
                    environment=settings.ENVIRONMENT.value,
                )
                llm_calls_num += 1
                continue
            except Exception as e:
                logger.error(
                    "azure_openai_general_error",
                    llm_calls_num=llm_calls_num,
                    attempt=attempt + 1,
                    max_retries=max_retries,
                    error=str(e),
                    error_type=type(e).__name__,
                    deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
                    environment=settings.ENVIRONMENT.value,
                )
                llm_calls_num += 1
                continue

        raise Exception(f"Failed to get a response from Azure OpenAI after {max_retries} attempts")

    # Define our tool node
    async def _tool_call(self, state: GraphState) -> GraphState:
        """Process tool calls from the last message.

        Args:
            state: The current agent state containing messages and tool calls.

        Returns:
            Dict with updated messages containing tool responses.
        """
        outputs = []
        for tool_call in state.messages[-1].tool_calls:
            tool_result = await self.tools_by_name[tool_call["name"]].ainvoke(tool_call["args"])
            outputs.append(
                ToolMessage(
                    content=tool_result,
                    name=tool_call["name"],
                    tool_call_id=tool_call["id"],
                )
            )
        return {"messages": outputs}



    def _route_to_team(self, state: IcmsState) -> Literal["DOC_INTENT", "SCHED_INTENT", "CROSS_INTENT", "clarification", "fallback"]:
        """Route to appropriate team based on intent classification.

        Args:
            state: The current ICMS state containing intent classification.

        Returns:
            Team to route to based on intent
        """
        intent = state.get("intent", "SCHED_INTENT")
        requires_clarify = state.get("requires_clarify", False)

        # Check for errors or clarification needs
        if requires_clarify:
            logger.info("routing_to_clarification", intent=intent)
            return "clarification"

        # Check for unknown or problematic intents
        if intent == "unknown" or intent is None:
            logger.info("routing_to_fallback", intent=intent)
            return "fallback"

        logger.info("routing_to_team", intent=intent)

        if intent == "DOC_INTENT":
            return "DOC_INTENT"
        elif intent == "CROSS_INTENT":
            return "CROSS_INTENT"
        else:
            return "SCHED_INTENT"  # Default to schedule team

    def _should_continue(self, state: GraphState) -> Literal["end", "continue"]:
        """Determine if the agent should continue or end based on the last message.

        Args:
            state: The current agent state containing messages.

        Returns:
            Literal["end", "continue"]: "end" if there are no tool calls, "continue" otherwise.
        """
        messages = state.messages
        last_message = messages[-1]
        # If there is no function call, then we finish
        if not last_message.tool_calls:
            return "end"
        # Otherwise if there is, we continue
        else:
            return "continue"

    async def create_graph(self) -> Optional[CompiledStateGraph]:
        """Create and configure the ICMS Multi-Agent LangGraph workflow.

        Returns:
            Optional[CompiledStateGraph]: The configured LangGraph instance or None if init fails
        """
        if self._graph is None:
            try:
                logger.info("creating_icms_multi_agent_graph",
                           environment=settings.ENVIRONMENT.value)

                # Use IcmsState for the new architecture
                graph_builder = StateGraph(IcmsState)

                # Add ACL filter node first
                graph_builder.add_node("acl_filter", acl_filter_node)

                # Add supervisor node for intent classification
                graph_builder.add_node("supervisor", classify_intent_node)

                # Add error handling and clarification nodes
                graph_builder.add_node("clarification", clarification_node)
                graph_builder.add_node("fallback", fallback_node)

                # Add sub-graphs for specialized teams
                doc_team_graph = create_document_team_subgraph()
                schedule_team_graph = create_schedule_team_subgraph()
                cross_module_graph = create_cross_module_subgraph()

                graph_builder.add_node("doc_team", doc_team_graph)
                graph_builder.add_node("schedule_team", schedule_team_graph)
                graph_builder.add_node("cross_module", cross_module_graph)

                logger.info("icms_graph_nodes_added",
                           nodes=["acl_filter", "supervisor", "clarification", "fallback", "doc_team", "schedule_team", "cross_module"])

                # Set up the workflow: acl_filter -> supervisor -> appropriate team/handler
                graph_builder.add_edge("acl_filter", "supervisor")

                graph_builder.add_conditional_edges(
                    "supervisor",
                    self._route_to_team,
                    {
                        "DOC_INTENT": "doc_team",
                        "SCHED_INTENT": "schedule_team",
                        "CROSS_INTENT": "cross_module",
                        "clarification": "clarification",
                        "fallback": "fallback"
                    },
                )

                # All teams and handlers end the workflow
                graph_builder.add_edge("doc_team", END)
                graph_builder.add_edge("schedule_team", END)
                graph_builder.add_edge("cross_module", END)
                graph_builder.add_edge("clarification", END)
                graph_builder.add_edge("fallback", END)

                # Start with ACL filter
                graph_builder.set_entry_point("acl_filter")

                logger.info("icms_graph_workflow_configured",
                           entry_point="supervisor",
                           environment=settings.ENVIRONMENT.value)

                # Choose checkpointer based on environment
                if settings.ENVIRONMENT == Environment.DEVELOPMENT:
                    # Use MemorySaver for development (no Redis Stack required)
                    checkpointer = MemorySaver()
                    logger.info("development_memory_checkpointer", environment=settings.ENVIRONMENT.value)
                else:
                    # Use Redis checkpointer for staging/production
                    redis_client = await self._get_redis_client()
                    if redis_client:
                        try:
                            checkpointer = AsyncRedisSaver(redis_url=settings.REDIS_URL)
                            logger.info("redis_checkpointer_initialized", environment=settings.ENVIRONMENT.value)
                        except Exception as e:
                            logger.error("redis_checkpointer_failed", error=str(e), environment=settings.ENVIRONMENT.value)
                            # Fallback to memory checkpointer
                            checkpointer = MemorySaver()
                            logger.warning("fallback_memory_checkpointer", environment=settings.ENVIRONMENT.value)
                    else:
                        # In production, proceed without checkpointer if needed
                        checkpointer = None
                        if settings.ENVIRONMENT != Environment.PRODUCTION:
                            raise Exception("Redis client initialization failed")

                self._graph = graph_builder.compile(
                    checkpointer=checkpointer,
                    name=f"ICMS Multi-Agent System ({settings.ENVIRONMENT.value})"
                )

                logger.info(
                    "icms_multi_agent_graph_created",
                    graph_name="ICMS Multi-Agent System",
                    environment=settings.ENVIRONMENT.value,
                    has_checkpointer=checkpointer is not None,
                )
            except Exception as e:
                logger.error("graph_creation_failed", error=str(e), environment=settings.ENVIRONMENT.value)
                # In production, we don't want to crash the app
                if settings.ENVIRONMENT == Environment.PRODUCTION:
                    logger.warning("continuing_without_graph")
                    return None
                raise e

        return self._graph

    async def get_response(
        self,
        messages: list[Message],
        session_id: str,
        user_id: Optional[str] = None,
        schedule_id: Optional[str] = None,
        auth_token: Optional[str] = None,
    ) -> list[dict]:
        """Get a response from the ICMS Multi-Agent System.

        Args:
            messages (list[Message]): The messages to send to the system.
            session_id (str): The session ID for tracking.
            user_id (Optional[str]): The user ID for tracking.
            schedule_id (Optional[str]): Optional schedule ID for context.
            auth_token (Optional[str]): Optional GraphQL auth token for backend API access.

        Returns:
            list[dict]: The response from the multi-agent system.
        """
        if self._graph is None:
            self._graph = await self.create_graph()

        config = {
            "configurable": {"thread_id": session_id},
            "callbacks": [CallbackHandler()],
            "metadata": {
                "user_id": user_id,
                "session_id": session_id,
                "schedule_id": schedule_id,
                "auth_token": auth_token,
                "environment": settings.ENVIRONMENT.value,
                "debug": False,
            },
        }

        # Check semantic cache first
        if messages:
            latest_message = messages[-1]
            cached_response = await semantic_cache.get_cached_response(
                latest_message.content,
                "unknown",  # Intent not classified yet
                {"project_id": schedule_id} if schedule_id else {}
            )

            if cached_response:
                logger.info("semantic_cache_hit_response", session_id=session_id)
                return self.__process_messages(cached_response.get("messages", []))

        # Prepare messages with last_10 optimization
        all_messages = dump_messages(messages)
        last_10_messages, full_history_id = await message_manager.prepare_state_messages(
            session_id, all_messages
        )

        # Prepare ICMS state
        icms_state = {
            "messages": last_10_messages,
            "full_history_id": full_history_id,
            "intent": None,
            "entities": {"project_id": schedule_id} if schedule_id else {},
            "gql_response": None,
            "requires_clarify": False,
            "user_jwt": auth_token or "",
            "user_permissions": {}  # Will be populated by ACL filter
        }

        try:
            response = await self._graph.ainvoke(icms_state, config)
            processed_response = self.__process_messages(response["messages"])

            # Cache the response for future similar queries
            if messages and response.get("intent"):
                await semantic_cache.store_response(
                    messages[-1].content,
                    response["intent"],
                    response.get("entities", {}),
                    {"messages": response["messages"]}
                )

            return processed_response
        except Exception as e:
            logger.error(f"Error getting ICMS response: {str(e)}")
            raise e

    async def get_stream_response(
        self, messages: list[Message], session_id: str, user_id: Optional[str] = None, schedule_id: Optional[str] = None, auth_token: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """Get a stream response from the ICMS Multi-Agent System.

        Args:
            messages (list[Message]): The messages to send to the system.
            session_id (str): The session ID for the conversation.
            user_id (Optional[str]): The user ID for the conversation.
            schedule_id (Optional[str]): Optional schedule ID for context.
            auth_token (Optional[str]): Optional GraphQL auth token for backend API access.

        Yields:
            str: Tokens of the multi-agent system response.
        """
        config = {
            "configurable": {"thread_id": session_id},
            "callbacks": [
                CallbackHandler(
                    environment=settings.ENVIRONMENT.value, debug=False, user_id=user_id, session_id=session_id
                )
            ],
        }
        if self._graph is None:
            self._graph = await self.create_graph()

        # Prepare ICMS state
        icms_state = {
            "messages": dump_messages(messages),
            "intent": None,
            "entities": {"project_id": schedule_id} if schedule_id else {},
            "gql_response": None,
            "requires_clarify": False,
            "user_jwt": auth_token or ""
        }

        try:
            async for token, _ in self._graph.astream(icms_state, config, stream_mode="messages"):
                try:
                    yield token.content
                except Exception as token_error:
                    logger.error("Error processing token", error=str(token_error), session_id=session_id)
                    # Continue with next token even if current one fails
                    continue
        except Exception as stream_error:
            logger.error("Error in ICMS stream processing", error=str(stream_error), session_id=session_id)
            raise stream_error

    async def get_chat_history(self, session_id: str) -> list[Message]:
        """Get the chat history for a given thread ID.

        Args:
            session_id (str): The session ID for the conversation.

        Returns:
            list[Message]: The chat history.
        """
        if self._graph is None:
            self._graph = await self.create_graph()

        state: StateSnapshot = await sync_to_async(self._graph.get_state)(
            config={"configurable": {"thread_id": session_id}}
        )
        return self.__process_messages(state.values["messages"]) if state.values else []

    def __process_messages(self, messages: list[BaseMessage]) -> list[Message]:
        openai_style_messages = convert_to_openai_messages(messages)
        # keep just assistant and user messages
        return [
            Message(**message)
            for message in openai_style_messages
            if message["role"] in ["assistant", "user"] and message["content"]
        ]

    async def clear_chat_history(self, session_id: str) -> None:
        """Clear all chat history for a given thread ID.

        Args:
            session_id: The ID of the session to clear history for.

        Raises:
            Exception: If there's an error clearing the chat history.
        """
        try:
            # Get Redis client
            redis_client = await self._get_redis_client()

            if redis_client:
                # Clear all keys related to this session
                pattern = f"*{session_id}*"
                keys = await redis_client.keys(pattern)

                if keys:
                    await redis_client.delete(*keys)
                    logger.info(f"Cleared {len(keys)} Redis keys for session {session_id}")
                else:
                    logger.info(f"No Redis keys found for session {session_id}")
            else:
                logger.warning("Redis client not available, cannot clear chat history")

        except Exception as e:
            logger.error("Failed to clear chat history", error=str(e))
            raise
