"""Shared scratchpad node for ICMS Multi-Agent System.

This module implements a shared scratchpad that allows cross-module tools
to write once and read everywhere, enabling better coordination between
document and schedule teams.
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

import redis.asyncio as redis

from app.core.config import settings
from app.core.logging import logger
from app.schemas import IcmsState


class SharedScratchpad:
    """Shared memory space for cross-module coordination."""
    
    def __init__(self):
        self._redis_client: Optional[redis.Redis] = None
        self.ttl = 3600  # 1 hour TTL for scratchpad data
    
    async def _get_redis_client(self) -> redis.Redis:
        """Get Redis client for scratchpad storage."""
        if self._redis_client is None:
            self._redis_client = redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
            )
        return self._redis_client
    
    async def write(self, session_id: str, key: str, data: Any, namespace: str = "global") -> bool:
        """Write data to the shared scratchpad.
        
        Args:
            session_id: Session identifier
            key: Data key
            data: Data to store
            namespace: Namespace for organization (global, documents, schedule, etc.)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            redis_client = await self._get_redis_client()
            
            # Create scratchpad key
            scratchpad_key = f"icms:scratchpad:{session_id}:{namespace}:{key}"
            
            # Prepare data with metadata
            scratchpad_entry = {
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "namespace": namespace,
                "key": key,
                "session_id": session_id
            }
            
            # Store with TTL
            await redis_client.setex(
                scratchpad_key,
                self.ttl,
                json.dumps(scratchpad_entry)
            )
            
            logger.info(
                "scratchpad_write",
                session_id=session_id,
                key=key,
                namespace=namespace,
                data_type=type(data).__name__
            )
            
            return True
            
        except Exception as e:
            logger.error("scratchpad_write_error", error=str(e), session_id=session_id, key=key)
            return False
    
    async def read(self, session_id: str, key: str, namespace: str = "global") -> Optional[Any]:
        """Read data from the shared scratchpad.
        
        Args:
            session_id: Session identifier
            key: Data key
            namespace: Namespace to read from
            
        Returns:
            Stored data or None if not found
        """
        try:
            redis_client = await self._get_redis_client()
            
            # Create scratchpad key
            scratchpad_key = f"icms:scratchpad:{session_id}:{namespace}:{key}"
            
            # Retrieve data
            entry_data = await redis_client.get(scratchpad_key)
            
            if entry_data:
                entry = json.loads(entry_data)
                
                logger.info(
                    "scratchpad_read",
                    session_id=session_id,
                    key=key,
                    namespace=namespace,
                    found=True
                )
                
                return entry["data"]
            else:
                logger.debug(
                    "scratchpad_read",
                    session_id=session_id,
                    key=key,
                    namespace=namespace,
                    found=False
                )
                return None
                
        except Exception as e:
            logger.error("scratchpad_read_error", error=str(e), session_id=session_id, key=key)
            return None
    
    async def list_keys(self, session_id: str, namespace: str = "*") -> List[str]:
        """List all keys in the scratchpad for a session.
        
        Args:
            session_id: Session identifier
            namespace: Namespace pattern (* for all)
            
        Returns:
            List of available keys
        """
        try:
            redis_client = await self._get_redis_client()
            
            # Create pattern
            pattern = f"icms:scratchpad:{session_id}:{namespace}:*"
            
            # Get matching keys
            keys = await redis_client.keys(pattern)
            
            # Extract just the key part
            extracted_keys = []
            for key in keys:
                parts = key.split(":")
                if len(parts) >= 5:
                    extracted_keys.append(parts[-1])  # Last part is the actual key
            
            logger.info(
                "scratchpad_list_keys",
                session_id=session_id,
                namespace=namespace,
                key_count=len(extracted_keys)
            )
            
            return extracted_keys
            
        except Exception as e:
            logger.error("scratchpad_list_keys_error", error=str(e), session_id=session_id)
            return []
    
    async def clear_namespace(self, session_id: str, namespace: str) -> bool:
        """Clear all data in a namespace for a session.
        
        Args:
            session_id: Session identifier
            namespace: Namespace to clear
            
        Returns:
            True if successful, False otherwise
        """
        try:
            redis_client = await self._get_redis_client()
            
            # Create pattern
            pattern = f"icms:scratchpad:{session_id}:{namespace}:*"
            
            # Get matching keys
            keys = await redis_client.keys(pattern)
            
            if keys:
                await redis_client.delete(*keys)
            
            logger.info(
                "scratchpad_namespace_cleared",
                session_id=session_id,
                namespace=namespace,
                cleared_count=len(keys)
            )
            
            return True
            
        except Exception as e:
            logger.error("scratchpad_clear_namespace_error", error=str(e), session_id=session_id)
            return False


def shared_scratchpad_node(state: IcmsState) -> IcmsState:
    """Shared scratchpad node for cross-module coordination.
    
    This node initializes the scratchpad and provides access to shared data
    for all subsequent agents and tools.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with scratchpad access
    """
    logger.info("shared_scratchpad_node_started")
    
    try:
        # Initialize scratchpad
        scratchpad = SharedScratchpad()
        
        # Extract session ID from full_history_id or generate one
        session_id = state.get("full_history_id", "default_session")
        if ":" in session_id:
            session_id = session_id.split(":")[-1]  # Extract session part
        
        # Store scratchpad reference in state
        updated_state = {
            **state,
            "scratchpad": scratchpad,
            "scratchpad_session_id": session_id
        }
        
        logger.info(
            "shared_scratchpad_initialized",
            session_id=session_id
        )
        
        return updated_state
        
    except Exception as e:
        logger.error("shared_scratchpad_node_error", error=str(e))
        # Return state without scratchpad on error
        return state


async def write_to_scratchpad(
    state: IcmsState, 
    key: str, 
    data: Any, 
    namespace: str = "global"
) -> bool:
    """Helper function to write to scratchpad from tools.
    
    Args:
        state: Current ICMS state
        key: Data key
        data: Data to store
        namespace: Namespace for organization
        
    Returns:
        True if successful, False otherwise
    """
    scratchpad = state.get("scratchpad")
    session_id = state.get("scratchpad_session_id")
    
    if scratchpad and session_id:
        return await scratchpad.write(session_id, key, data, namespace)
    else:
        logger.warning("scratchpad_not_available", key=key, namespace=namespace)
        return False


async def read_from_scratchpad(
    state: IcmsState, 
    key: str, 
    namespace: str = "global"
) -> Optional[Any]:
    """Helper function to read from scratchpad in tools.
    
    Args:
        state: Current ICMS state
        key: Data key
        namespace: Namespace to read from
        
    Returns:
        Stored data or None if not found
    """
    scratchpad = state.get("scratchpad")
    session_id = state.get("scratchpad_session_id")
    
    if scratchpad and session_id:
        return await scratchpad.read(session_id, key, namespace)
    else:
        logger.warning("scratchpad_not_available_for_read", key=key, namespace=namespace)
        return None


# Common scratchpad keys for cross-module coordination
class ScratchpadKeys:
    """Common keys used in the shared scratchpad."""
    
    # Project context
    CURRENT_PROJECT = "current_project"
    PROJECT_METADATA = "project_metadata"
    
    # Document context
    RECENT_DOCUMENTS = "recent_documents"
    DOCUMENT_ANALYSIS = "document_analysis"
    APPROVAL_STATUS = "approval_status"
    
    # Schedule context
    ACTIVE_TASKS = "active_tasks"
    CRITICAL_PATH = "critical_path"
    RESOURCE_CONFLICTS = "resource_conflicts"
    
    # Cross-module analysis
    IMPACT_ANALYSIS = "impact_analysis"
    DEPENDENCY_MAP = "dependency_map"
    RISK_ASSESSMENT = "risk_assessment"
    
    # User preferences
    USER_PREFERENCES = "user_preferences"
    QUERY_HISTORY = "query_history"


# Global scratchpad instance
shared_scratchpad = SharedScratchpad()
