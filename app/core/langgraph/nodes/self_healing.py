"""Self-healing retry node for ICMS Multi-Agent System.

This module implements self-healing retry logic that retries with tweaked
prompts on 5xx errors and other recoverable failures.
"""

import asyncio
import random
from typing import Any, Dict, List, Optional

from langchain_core.messages import AIMessage, HumanMessage

from app.core.logging import logger
from app.schemas import IcmsState


class SelfHealingRetry:
    """Self-healing retry mechanism with adaptive prompt tweaking."""
    
    def __init__(self):
        self.max_retries = 3
        self.base_delay = 1.0
        self.max_delay = 10.0
        
        # Error patterns that trigger self-healing
        self.recoverable_errors = [
            "5xx", "timeout", "connection", "rate_limit", 
            "temporary", "unavailable", "overloaded"
        ]
        
        # Prompt tweaking strategies
        self.prompt_tweaks = [
            "simplify_query",
            "add_context",
            "break_down_request",
            "rephrase_question",
            "add_fallback_options"
        ]
    
    def is_recoverable_error(self, error: str) -> bool:
        """Check if an error is recoverable.
        
        Args:
            error: Error message or description
            
        Returns:
            True if error is recoverable, False otherwise
        """
        error_lower = error.lower()
        return any(pattern in error_lower for pattern in self.recoverable_errors)
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay with jitter.
        
        Args:
            attempt: Current attempt number (0-based)
            
        Returns:
            Delay in seconds
        """
        # Exponential backoff with jitter
        delay = min(self.base_delay * (2 ** attempt), self.max_delay)
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter
    
    def tweak_prompt(self, original_query: str, strategy: str, context: dict) -> str:
        """Tweak the prompt based on the selected strategy.
        
        Args:
            original_query: Original user query
            strategy: Tweaking strategy to apply
            context: Additional context for tweaking
            
        Returns:
            Tweaked prompt
        """
        if strategy == "simplify_query":
            return f"Please provide a simple answer to: {original_query}"
        
        elif strategy == "add_context":
            project_context = context.get("project_context", "")
            return f"Context: {project_context}\n\nQuery: {original_query}"
        
        elif strategy == "break_down_request":
            return f"Let's break this down step by step: {original_query}. Start with the most important part."
        
        elif strategy == "rephrase_question":
            return f"In other words: {original_query}. Please provide the key information."
        
        elif strategy == "add_fallback_options":
            return f"{original_query}\n\nIf exact information isn't available, please provide the closest relevant information or suggest alternatives."
        
        else:
            return original_query
    
    async def retry_with_healing(
        self, 
        operation_func: callable,
        state: IcmsState,
        error: Exception,
        attempt: int = 0
    ) -> Any:
        """Retry an operation with self-healing.
        
        Args:
            operation_func: Function to retry
            state: Current ICMS state
            error: Original error that triggered retry
            attempt: Current attempt number
            
        Returns:
            Result of successful operation or final error
        """
        if attempt >= self.max_retries:
            logger.error(
                "self_healing_max_retries_exceeded",
                max_retries=self.max_retries,
                final_error=str(error)
            )
            raise error
        
        if not self.is_recoverable_error(str(error)):
            logger.info(
                "self_healing_non_recoverable_error",
                error=str(error),
                attempt=attempt
            )
            raise error
        
        # Calculate delay
        delay = self.calculate_delay(attempt)
        
        logger.info(
            "self_healing_retry_attempt",
            attempt=attempt + 1,
            max_retries=self.max_retries,
            delay=delay,
            error=str(error)
        )
        
        # Wait before retry
        await asyncio.sleep(delay)
        
        # Apply prompt tweaking if this is a query-related operation
        tweaked_state = self._apply_prompt_tweaking(state, attempt)
        
        try:
            # Retry the operation
            result = await operation_func(tweaked_state)
            
            logger.info(
                "self_healing_retry_success",
                attempt=attempt + 1,
                strategy_used=tweaked_state.get("healing_strategy")
            )
            
            return result
            
        except Exception as retry_error:
            logger.warning(
                "self_healing_retry_failed",
                attempt=attempt + 1,
                retry_error=str(retry_error)
            )
            
            # Recursive retry
            return await self.retry_with_healing(
                operation_func, 
                state, 
                retry_error, 
                attempt + 1
            )
    
    def _apply_prompt_tweaking(self, state: IcmsState, attempt: int) -> IcmsState:
        """Apply prompt tweaking based on attempt number.
        
        Args:
            state: Current ICMS state
            attempt: Current attempt number
            
        Returns:
            State with tweaked prompts
        """
        # Select strategy based on attempt
        strategy = self.prompt_tweaks[attempt % len(self.prompt_tweaks)]
        
        # Get original query
        user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
        if not user_messages:
            return state
        
        original_query = user_messages[-1].content
        
        # Build context for tweaking
        context = {
            "project_context": state.get("entities", {}).get("project_id", ""),
            "conversation_topics": state.get("entities", {}).get("conversation_topics", []),
            "intent": state.get("intent", ""),
            "attempt": attempt
        }
        
        # Tweak the prompt
        tweaked_query = self.tweak_prompt(original_query, strategy, context)
        
        # Create new message list with tweaked query
        tweaked_messages = state["messages"][:-1] + [HumanMessage(content=tweaked_query)]
        
        logger.info(
            "prompt_tweaking_applied",
            strategy=strategy,
            attempt=attempt,
            original_preview=original_query[:50],
            tweaked_preview=tweaked_query[:50]
        )
        
        return {
            **state,
            "messages": tweaked_messages,
            "healing_strategy": strategy,
            "healing_attempt": attempt
        }


def self_healing_retry_node(state: IcmsState) -> IcmsState:
    """Self-healing retry node for error recovery.
    
    This node is called when other nodes encounter recoverable errors
    and need to retry with adaptive strategies.
    
    Args:
        state: Current ICMS state with error information
        
    Returns:
        Updated state with retry strategy
    """
    logger.info("self_healing_retry_node_started")
    
    try:
        healer = SelfHealingRetry()
        
        # Check if there's an error to heal from
        last_error = state.get("last_error")
        if not last_error:
            logger.warning("self_healing_no_error_to_heal")
            return state
        
        # Check if error is recoverable
        if not healer.is_recoverable_error(str(last_error)):
            logger.info("self_healing_non_recoverable", error=str(last_error))
            return {
                **state,
                "healing_status": "non_recoverable",
                "healing_recommendation": "manual_intervention"
            }
        
        # Apply healing strategy
        attempt = state.get("healing_attempt", 0)
        healed_state = healer._apply_prompt_tweaking(state, attempt)
        
        # Add healing metadata
        healed_state.update({
            "healing_status": "applied",
            "healing_attempt": attempt + 1,
            "original_error": str(last_error),
            "healing_strategy": healed_state.get("healing_strategy")
        })
        
        logger.info(
            "self_healing_strategy_applied",
            strategy=healed_state.get("healing_strategy"),
            attempt=attempt + 1,
            error=str(last_error)
        )
        
        return healed_state
        
    except Exception as e:
        logger.error("self_healing_node_error", error=str(e))
        return {
            **state,
            "healing_status": "failed",
            "healing_error": str(e)
        }


class HealthMonitor:
    """Monitor system health and trigger self-healing when needed."""
    
    def __init__(self):
        self.error_counts = {}
        self.success_counts = {}
        self.health_threshold = 0.7  # 70% success rate threshold
    
    def record_operation(self, operation: str, success: bool, error: Optional[str] = None):
        """Record operation result for health monitoring.
        
        Args:
            operation: Operation name
            success: Whether operation was successful
            error: Error message if failed
        """
        if operation not in self.error_counts:
            self.error_counts[operation] = 0
            self.success_counts[operation] = 0
        
        if success:
            self.success_counts[operation] += 1
        else:
            self.error_counts[operation] += 1
        
        # Log health status
        total_ops = self.error_counts[operation] + self.success_counts[operation]
        success_rate = self.success_counts[operation] / total_ops if total_ops > 0 else 1.0
        
        logger.info(
            "health_monitor_record",
            operation=operation,
            success=success,
            success_rate=success_rate,
            total_operations=total_ops,
            error=error
        )
        
        # Trigger alert if health is poor
        if total_ops >= 10 and success_rate < self.health_threshold:
            logger.warning(
                "health_monitor_alert",
                operation=operation,
                success_rate=success_rate,
                threshold=self.health_threshold,
                recommendation="consider_self_healing"
            )
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall health status.
        
        Returns:
            Dictionary with health metrics
        """
        health_status = {}
        
        for operation in self.error_counts:
            total_ops = self.error_counts[operation] + self.success_counts[operation]
            success_rate = self.success_counts[operation] / total_ops if total_ops > 0 else 1.0
            
            health_status[operation] = {
                "success_rate": success_rate,
                "total_operations": total_ops,
                "error_count": self.error_counts[operation],
                "success_count": self.success_counts[operation],
                "health_status": "healthy" if success_rate >= self.health_threshold else "unhealthy"
            }
        
        return health_status


# Global instances
self_healing_retry = SelfHealingRetry()
health_monitor = HealthMonitor()
