"""Intent classifier node for the ICMS Multi-Agent System.

This module implements the supervisor node for intent classification using LLM
as specified in the development blueprint.
"""

import json
from typing import Dict, List, Optional

from langchain_core.messages import HumanMessage
from langchain_openai import AzureChatOpenAI
from pydantic import BaseModel, Field

from app.core.config import settings
from app.core.langgraph.entities import Project, Task, Document
from app.core.logging import logger
from app.core.langgraph.utils import (
    analyze_chat_history,
    get_contextual_entities,
    should_use_previous_context,
    format_context_summary,
)
from app.schemas import IcmsState


class IntentClassificationResult(BaseModel):
    """Result of intent classification with enhanced confidence scoring."""

    intent: str = Field(..., description="Classified intent (DOC_INTENT, SCHED_INTENT, CROSS_INTENT)")
    entities: Dict = Field(default_factory=dict, description="Extracted entities from the query")
    confidence: float = Field(..., description="Confidence score for the classification (0.0-1.0)")
    reasoning: str = Field(..., description="Reasoning for the classification")
    alternative_intents: List[Dict] = Field(default_factory=list, description="Alternative intents with scores")
    requires_clarification: bool = Field(default=False, description="Whether clarification is needed")

    @property
    def is_high_confidence(self) -> bool:
        """Check if confidence is above threshold."""
        return self.confidence > 0.7

    @property
    def needs_clarification(self) -> bool:
        """Check if clarification is needed based on confidence."""
        return self.confidence <= 0.7 or self.requires_clarification


class EntityExtraction(BaseModel):
    """Extracted entities from user query."""

    project_id: Optional[int] = Field(None, description="Project ID mentioned in query (integer)")
    project_schedule_id: Optional[int] = Field(None, description="Project schedule ID mentioned in query (integer)")
    project_name: Optional[str] = Field(None, description="Project name mentioned in query")
    date_range: Optional[Dict] = Field(None, description="Date range mentioned in query")
    document_type: Optional[str] = Field(None, description="Type of document mentioned")
    task_name: Optional[str] = Field(None, description="Task name mentioned in query")
    status: Optional[str] = Field(None, description="Status mentioned in query")
    assignee: Optional[str] = Field(None, description="Assignee mentioned in query")


# Intent classification prompt
INTENT_CLASSIFICATION_PROMPT = """You are an expert intent classifier for an Integrated Construction Management System (ICMS).

Your task is to analyze user queries and classify them into one of three intents:

1. **DOC_INTENT**: Queries about documents, drawings, specifications, approvals, or document-related tasks
   - Examples: "Show me structural drawings", "Find approved documents", "What documents are pending?"

2. **SCHED_INTENT**: Queries about schedules, tasks, timelines, critical path, or schedule-related activities  
   - Examples: "Show overdue tasks", "What's the critical path?", "Tasks starting this month"

3. **CROSS_INTENT**: Queries that require both document and schedule information or analysis
   - Examples: "What documents are blocking next week's tasks?", "Impact of drawing changes on schedule"

Additionally, extract relevant entities from the query such as:
- Project names/IDs
- Date ranges
- Document types
- Task names
- Status values
- Assignee names

Analyze this user query: "{query}"

Respond with a JSON object containing:
- intent: One of DOC_INTENT, SCHED_INTENT, or CROSS_INTENT
- entities: Dictionary of extracted entities
- confidence: Float between 0.0 and 1.0 (be conservative - use ≤0.7 when uncertain)
- reasoning: Brief explanation of your classification
- alternative_intents: List of other possible intents with scores
- requires_clarification: Boolean if query is ambiguous

Example response:
{{
    "intent": "SCHED_INTENT",
    "entities": {{
        "date_range": "this month",
        "status": "overdue"
    }},
    "confidence": 0.9,
    "reasoning": "Query asks about overdue tasks with temporal context, clearly schedule-related",
    "alternative_intents": [
        {{"intent": "CROSS_INTENT", "confidence": 0.2, "reason": "Could involve document dependencies"}}
    ],
    "requires_clarification": false
}}"""


def classify_intent_node(state: IcmsState) -> IcmsState:
    """Classify user intent using LLM with chat history context.

    Args:
        state: Current ICMS state containing messages

    Returns:
        Updated state with intent and entities
    """
    logger.info("intent_classification_started")

    try:
        # Analyze chat history for context (-10 messages)
        conversation_context = analyze_chat_history(state["messages"], limit=10)
        context_summary = format_context_summary(conversation_context)

        # Get the latest user message
        user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]

        if not user_messages:
            logger.warning("no_user_messages_found")
            return {
                **state,
                "intent": "SCHED_INTENT",  # Default fallback
                "entities": {},
                "requires_clarify": False
            }

        latest_query = user_messages[-1].content
        use_context = should_use_previous_context(conversation_context, latest_query)

        logger.info(
            "classifying_query_with_context",
            query_preview=latest_query[:100],
            context_summary=context_summary,
            use_context=use_context
        )
        
        # Initialize LLM with function calling for multilingual support
        llm = AzureChatOpenAI(
            azure_deployment=settings.AZURE_OPENAI_DEPLOYMENT_NAME,
            api_version=settings.AZURE_OPENAI_API_VERSION,
            azure_endpoint=settings.AZURE_OPENAI_ENDPOINT,
            api_key=settings.AZURE_OPENAI_API_KEY,
            temperature=0.1,  # Low temperature for consistent classification
            max_tokens=500,
        )

        # Bind function calling schema for structured output
        llm_with_tools = llm.bind_tools([IntentClassificationResult])
        
        # Create multilingual prompt for function calling
        multilingual_prompt = f"""
        Classify the user's intent regardless of language (English, Malay, Chinese, etc.).

        User Query: {latest_query}
        {f"Context: {context_summary}" if use_context else ""}

        Classify into:
        - DOC_INTENT: Documents, drawings, specifications, files
        - SCHED_INTENT: Schedule, tasks, timeline, deadlines
        - CROSS_INTENT: Cross-module analysis, dependencies, impact

        Extract entities and provide confidence score (≤0.7 if uncertain).
        """

        # Get structured classification result via function calling
        response = llm_with_tools.invoke([HumanMessage(content=multilingual_prompt)])
        
        # Parse function calling response
        try:
            if response.tool_calls:
                # Extract from function call
                tool_call = response.tool_calls[0]
                result = IntentClassificationResult(**tool_call["args"])
            else:
                # Fallback to JSON parsing
                result_data = json.loads(response.content)
                result = IntentClassificationResult(**result_data)
            
            # Enhance entities with conversation context
            enhanced_entities = get_contextual_entities(conversation_context, result.entities)

            logger.info(
                "intent_classified_with_context",
                intent=result.intent,
                confidence=result.confidence,
                entities_count=len(enhanced_entities),
                reasoning=result.reasoning,
                context_used=use_context
            )

            return {
                **state,
                "intent": result.intent,
                "entities": enhanced_entities,
                "requires_clarify": result.needs_clarification,  # Enhanced confidence-based clarification
                "confidence_score": result.confidence,
                "alternative_intents": result.alternative_intents
            }
            
        except (json.JSONDecodeError, ValueError) as e:
            logger.error("intent_classification_parse_error", error=str(e), response=response.content)
            # Fallback to simple keyword-based classification
            return _fallback_classification(state, latest_query)
            
    except Exception as e:
        logger.error("intent_classification_error", error=str(e))
        # Fallback to simple keyword-based classification
        return _fallback_classification(state, latest_query if 'latest_query' in locals() else "")


def _fallback_classification(state: IcmsState, query: str) -> IcmsState:
    """Fallback intent classification using simple keyword matching.
    
    Args:
        state: Current ICMS state
        query: User query string
        
    Returns:
        Updated state with fallback classification
    """
    query_lower = query.lower()
    
    # Simple keyword-based classification
    doc_keywords = ["document", "drawing", "specification", "approval", "file", "pdf", "dwg"]
    sched_keywords = ["task", "schedule", "timeline", "deadline", "overdue", "critical path", "milestone"]
    cross_keywords = ["impact", "blocking", "dependency", "affect", "relationship"]
    
    doc_score = sum(1 for keyword in doc_keywords if keyword in query_lower)
    sched_score = sum(1 for keyword in sched_keywords if keyword in query_lower)
    cross_score = sum(1 for keyword in cross_keywords if keyword in query_lower)
    
    if cross_score > 0:
        intent = "CROSS_INTENT"
    elif doc_score > sched_score:
        intent = "DOC_INTENT"
    else:
        intent = "SCHED_INTENT"  # Default to schedule
    
    logger.info(
        "fallback_classification_used",
        intent=intent,
        doc_score=doc_score,
        sched_score=sched_score,
        cross_score=cross_score
    )
    
    return {
        **state,
        "intent": intent,
        "entities": {},
        "requires_clarify": True  # Always require clarification for fallback
    }
