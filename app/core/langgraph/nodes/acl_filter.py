"""Row-level ACL filter node for ICMS Multi-Agent System.

This module implements row-level access control filtering before GraphQL queries
to prevent accidental over-fetching and ensure data security.
"""

import jwt
from typing import Dict, List, Optional, Set

from app.core.config import settings
from app.core.logging import logger
from app.schemas import IcmsState


class ACLFilter:
    """Access Control List filter for row-level security."""
    
    def __init__(self):
        self.permission_cache: Dict[str, dict] = {}
    
    def decode_jwt_permissions(self, jwt_token: str) -> dict:
        """Decode JWT token to extract user permissions.
        
        Args:
            jwt_token: JWT token from user
            
        Returns:
            Dictionary with user permissions
        """
        if not jwt_token:
            return {"user_id": None, "role": "anonymous", "projects": [], "permissions": []}
        
        try:
            # Decode JWT (in production, verify signature)
            payload = jwt.decode(
                jwt_token, 
                options={"verify_signature": False}  # For demo - verify in production
            )
            
            permissions = {
                "user_id": payload.get("sub") or payload.get("user_id"),
                "role": payload.get("role", "user"),
                "projects": payload.get("projects", []),
                "permissions": payload.get("permissions", []),
                "department": payload.get("department"),
                "company_id": payload.get("company_id"),
            }
            
            logger.info(
                "jwt_permissions_decoded",
                user_id=permissions["user_id"],
                role=permissions["role"],
                project_count=len(permissions["projects"])
            )
            
            return permissions
            
        except Exception as e:
            logger.error("jwt_decode_error", error=str(e))
            return {"user_id": None, "role": "anonymous", "projects": [], "permissions": []}
    
    def get_project_filters(self, permissions: dict, requested_project_id: Optional[str] = None) -> dict:
        """Generate project-level filters based on user permissions.
        
        Args:
            permissions: User permissions from JWT
            requested_project_id: Specific project requested
            
        Returns:
            GraphQL filters for project access
        """
        user_projects = permissions.get("projects", [])
        role = permissions.get("role", "user")
        
        # Admin can access all projects
        if role in ["admin", "super_admin"]:
            if requested_project_id:
                return {"projectId": {"eq": requested_project_id}}
            return {}  # No filter - can access all
        
        # Regular users can only access their assigned projects
        if not user_projects:
            logger.warning(
                "no_project_access",
                user_id=permissions.get("user_id"),
                role=role
            )
            return {"projectId": {"eq": "NO_ACCESS"}}  # Block all access
        
        # Filter to user's projects
        if requested_project_id:
            if requested_project_id in user_projects:
                return {"projectId": {"eq": requested_project_id}}
            else:
                logger.warning(
                    "project_access_denied",
                    user_id=permissions.get("user_id"),
                    requested_project=requested_project_id,
                    allowed_projects=user_projects
                )
                return {"projectId": {"eq": "NO_ACCESS"}}
        
        # Multiple projects allowed
        return {"projectId": {"in": user_projects}}
    
    def get_document_filters(self, permissions: dict) -> dict:
        """Generate document-level filters based on user permissions.
        
        Args:
            permissions: User permissions from JWT
            
        Returns:
            GraphQL filters for document access
        """
        role = permissions.get("role", "user")
        department = permissions.get("department")
        user_permissions = permissions.get("permissions", [])
        
        filters = {}
        
        # Role-based document access
        if role == "contractor":
            # Contractors can only see approved documents
            filters["status"] = {"eq": "approved"}
        elif role == "engineer":
            # Engineers can see draft and approved documents
            filters["status"] = {"in": ["draft", "approved", "under_review"]}
        elif role in ["project_manager", "admin"]:
            # PMs and admins can see all document statuses
            pass
        
        # Department-based filtering
        if department and "view_all_documents" not in user_permissions:
            filters["department"] = {"eq": department}
        
        # Confidentiality level filtering
        if "view_confidential" not in user_permissions:
            filters["confidentiality"] = {"ne": "confidential"}
        
        return filters
    
    def get_schedule_filters(self, permissions: dict) -> dict:
        """Generate schedule-level filters based on user permissions.
        
        Args:
            permissions: User permissions from JWT
            
        Returns:
            GraphQL filters for schedule access
        """
        role = permissions.get("role", "user")
        user_id = permissions.get("user_id")
        user_permissions = permissions.get("permissions", [])
        
        filters = {}
        
        # Role-based schedule access
        if role == "contractor":
            # Contractors can only see tasks assigned to them
            if user_id:
                filters["assignees"] = {"some": {"userId": {"eq": user_id}}}
        elif role == "site_supervisor":
            # Site supervisors can see tasks in their area
            department = permissions.get("department")
            if department:
                filters["department"] = {"eq": department}
        elif role in ["project_manager", "admin"]:
            # PMs and admins can see all tasks
            pass
        
        # Hide sensitive tasks unless authorized
        if "view_sensitive_tasks" not in user_permissions:
            filters["sensitivity"] = {"ne": "sensitive"}
        
        return filters
    
    def apply_acl_filters(self, base_filters: dict, permissions: dict, query_type: str) -> dict:
        """Apply ACL filters to base GraphQL filters.
        
        Args:
            base_filters: Base filters from query
            permissions: User permissions
            query_type: Type of query (documents, schedule, etc.)
            
        Returns:
            Enhanced filters with ACL constraints
        """
        acl_filters = {}
        
        # Get project filters
        requested_project = base_filters.get("projectId", {}).get("eq")
        project_filters = self.get_project_filters(permissions, requested_project)
        
        # Get type-specific filters
        if query_type == "documents":
            type_filters = self.get_document_filters(permissions)
        elif query_type == "schedule":
            type_filters = self.get_schedule_filters(permissions)
        else:
            type_filters = {}
        
        # Combine all filters
        combined_filters = {**base_filters, **project_filters, **type_filters}
        
        logger.info(
            "acl_filters_applied",
            query_type=query_type,
            user_id=permissions.get("user_id"),
            role=permissions.get("role"),
            filter_count=len(combined_filters)
        )
        
        return combined_filters


def acl_filter_node(state: IcmsState) -> IcmsState:
    """ACL filter node to apply row-level security before GraphQL queries.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with user permissions and ACL filters
    """
    logger.info("acl_filter_node_started")
    
    try:
        acl_filter = ACLFilter()
        jwt_token = state.get("user_jwt", "")
        
        # Decode JWT and extract permissions
        permissions = acl_filter.decode_jwt_permissions(jwt_token)
        
        # Store permissions in state for use by tools
        updated_state = {
            **state,
            "user_permissions": permissions
        }
        
        logger.info(
            "acl_filter_node_completed",
            user_id=permissions.get("user_id"),
            role=permissions.get("role"),
            project_count=len(permissions.get("projects", []))
        )
        
        return updated_state
        
    except Exception as e:
        logger.error("acl_filter_node_error", error=str(e))
        # On error, set minimal permissions
        return {
            **state,
            "user_permissions": {
                "user_id": None,
                "role": "anonymous", 
                "projects": [],
                "permissions": []
            }
        }


# Global ACL filter instance
acl_filter = ACLFilter()
