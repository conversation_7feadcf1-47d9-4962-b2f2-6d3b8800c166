"""Score-based router for ICMS Multi-Agent System.

This module implements a score-based router that chooses the best agent
when intents overlap or are ambiguous.
"""

from typing import Dict, List, Tuple

from app.core.logging import logger
from app.schemas import IcmsState


class AgentScorer:
    """Scores agents based on query characteristics and context."""
    
    def __init__(self):
        # Agent capability scores for different query types
        self.agent_capabilities = {
            "doc_team": {
                "document_keywords": 1.0,
                "file_operations": 1.0,
                "approval_workflows": 1.0,
                "version_control": 1.0,
                "metadata_analysis": 0.8,
                "search_operations": 0.9,
                "compliance_tracking": 0.6
            },
            "schedule_team": {
                "timeline_keywords": 1.0,
                "task_management": 1.0,
                "resource_planning": 1.0,
                "critical_path": 1.0,
                "progress_tracking": 0.9,
                "deadline_analysis": 0.9,
                "capacity_planning": 0.8
            },
            "cross_module": {
                "impact_analysis": 1.0,
                "dependency_mapping": 1.0,
                "risk_assessment": 1.0,
                "compliance_tracking": 1.0,
                "integration_queries": 0.9,
                "workflow_optimization": 0.8,
                "change_management": 0.7
            }
        }
        
        # Keyword mappings to capabilities
        self.keyword_mappings = {
            # Document keywords
            "document": "document_keywords",
            "drawing": "document_keywords", 
            "specification": "document_keywords",
            "file": "file_operations",
            "approval": "approval_workflows",
            "version": "version_control",
            "search": "search_operations",
            "find": "search_operations",
            
            # Schedule keywords
            "task": "task_management",
            "schedule": "timeline_keywords",
            "timeline": "timeline_keywords",
            "deadline": "deadline_analysis",
            "overdue": "deadline_analysis",
            "critical path": "critical_path",
            "resource": "resource_planning",
            "progress": "progress_tracking",
            
            # Cross-module keywords
            "impact": "impact_analysis",
            "dependency": "dependency_mapping",
            "affect": "impact_analysis",
            "block": "dependency_mapping",
            "risk": "risk_assessment",
            "compliance": "compliance_tracking",
            "relationship": "dependency_mapping"
        }
    
    def score_agents(self, query: str, intent: str, entities: dict, context: dict) -> Dict[str, float]:
        """Score all agents for the given query.
        
        Args:
            query: User query text
            intent: Classified intent
            entities: Extracted entities
            context: Conversation context
            
        Returns:
            Dictionary mapping agent names to scores
        """
        query_lower = query.lower()
        scores = {"doc_team": 0.0, "schedule_team": 0.0, "cross_module": 0.0}
        
        # Base intent scores
        intent_scores = {
            "DOC_INTENT": {"doc_team": 0.8, "schedule_team": 0.1, "cross_module": 0.3},
            "SCHED_INTENT": {"doc_team": 0.1, "schedule_team": 0.8, "cross_module": 0.3},
            "CROSS_INTENT": {"doc_team": 0.4, "schedule_team": 0.4, "cross_module": 0.9}
        }
        
        # Apply base intent scores
        if intent in intent_scores:
            for agent, score in intent_scores[intent].items():
                scores[agent] += score
        
        # Keyword-based scoring
        for keyword, capability in self.keyword_mappings.items():
            if keyword in query_lower:
                for agent, capabilities in self.agent_capabilities.items():
                    if capability in capabilities:
                        scores[agent] += capabilities[capability] * 0.3
        
        # Entity-based scoring
        scores = self._apply_entity_scoring(scores, entities)
        
        # Context-based scoring
        scores = self._apply_context_scoring(scores, context)
        
        # Confidence-based adjustment
        confidence = context.get("confidence_score", 1.0)
        if confidence <= 0.7:
            # Boost cross-module for low confidence queries
            scores["cross_module"] += 0.2
        
        # Normalize scores
        max_score = max(scores.values()) if scores.values() else 1.0
        if max_score > 0:
            scores = {agent: score / max_score for agent, score in scores.items()}
        
        logger.info(
            "agent_scoring_completed",
            intent=intent,
            confidence=confidence,
            scores=scores,
            query_preview=query[:50]
        )
        
        return scores
    
    def _apply_entity_scoring(self, scores: Dict[str, float], entities: dict) -> Dict[str, float]:
        """Apply entity-based scoring adjustments."""
        # Document-related entities
        if entities.get("document_type") or entities.get("file_format"):
            scores["doc_team"] += 0.3
        
        # Schedule-related entities
        if entities.get("date_range") or entities.get("task_status"):
            scores["schedule_team"] += 0.3
        
        # Cross-module indicators
        if entities.get("is_followup") or len(entities.get("conversation_topics", [])) > 1:
            scores["cross_module"] += 0.2
        
        return scores
    
    def _apply_context_scoring(self, scores: Dict[str, float], context: dict) -> Dict[str, float]:
        """Apply context-based scoring adjustments."""
        conversation_topics = context.get("conversation_topics", [])
        
        # Multi-topic conversations favor cross-module
        if len(conversation_topics) > 1:
            scores["cross_module"] += 0.3
        
        # Recent topic continuity
        last_intent = context.get("last_intent")
        if last_intent == "DOC_INTENT":
            scores["doc_team"] += 0.1
        elif last_intent == "SCHED_INTENT":
            scores["schedule_team"] += 0.1
        elif last_intent == "CROSS_INTENT":
            scores["cross_module"] += 0.1
        
        return scores
    
    def get_best_agent(self, scores: Dict[str, float], threshold: float = 0.1) -> str:
        """Get the best agent based on scores.
        
        Args:
            scores: Agent scores
            threshold: Minimum difference threshold for clear winner
            
        Returns:
            Best agent name
        """
        if not scores:
            return "schedule_team"  # Default fallback
        
        # Sort agents by score
        sorted_agents = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        
        best_agent, best_score = sorted_agents[0]
        second_best_score = sorted_agents[1][1] if len(sorted_agents) > 1 else 0.0
        
        # Check if there's a clear winner
        if best_score - second_best_score >= threshold:
            logger.info(
                "clear_agent_winner",
                best_agent=best_agent,
                best_score=best_score,
                second_best_score=second_best_score,
                margin=best_score - second_best_score
            )
            return best_agent
        else:
            # Close scores - use cross-module for ambiguous cases
            logger.info(
                "ambiguous_agent_choice",
                best_agent=best_agent,
                best_score=best_score,
                second_best_score=second_best_score,
                margin=best_score - second_best_score,
                fallback="cross_module"
            )
            return "cross_module"


def score_based_router_node(state: IcmsState) -> IcmsState:
    """Score-based router node for intelligent agent selection.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with routing decision and scores
    """
    logger.info("score_based_router_started")
    
    try:
        scorer = AgentScorer()
        
        # Get query and context
        from langchain_core.messages import HumanMessage
        user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
        
        if not user_messages:
            return {**state, "routing_decision": "schedule_team", "agent_scores": {}}
        
        query = user_messages[-1].content
        intent = state.get("intent", "SCHED_INTENT")
        entities = state.get("entities", {})
        
        # Build context
        context = {
            "confidence_score": state.get("confidence_score", 1.0),
            "conversation_topics": entities.get("conversation_topics", []),
            "last_intent": entities.get("last_intent"),
            "is_followup": entities.get("is_followup", False)
        }
        
        # Score all agents
        agent_scores = scorer.score_agents(query, intent, entities, context)
        
        # Get best agent
        best_agent = scorer.get_best_agent(agent_scores)
        
        # Map to routing decision
        routing_map = {
            "doc_team": "DOC_INTENT",
            "schedule_team": "SCHED_INTENT", 
            "cross_module": "CROSS_INTENT"
        }
        
        routing_decision = routing_map.get(best_agent, "SCHED_INTENT")
        
        logger.info(
            "score_based_routing_completed",
            best_agent=best_agent,
            routing_decision=routing_decision,
            agent_scores=agent_scores,
            query_preview=query[:50]
        )
        
        return {
            **state,
            "routing_decision": routing_decision,
            "agent_scores": agent_scores,
            "selected_agent": best_agent
        }
        
    except Exception as e:
        logger.error("score_based_router_error", error=str(e))
        # Fallback to original intent
        return {
            **state,
            "routing_decision": state.get("intent", "SCHED_INTENT"),
            "agent_scores": {},
            "selected_agent": "schedule_team"
        }
