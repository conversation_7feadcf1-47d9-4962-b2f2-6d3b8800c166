"""Error handling and clarification nodes for the ICMS Multi-Agent System.

This module implements fallback nodes and human-in-the-loop edges for error
handling and clarification requests as specified in the development blueprint.
"""

from typing import List

from langchain_core.messages import AIMessage, HumanMessage

from app.core.logging import logger
from app.schemas import IcmsState


def clarification_node(state: IcmsState) -> IcmsState:
    """Clarification node for handling ambiguous queries.
    
    This node generates clarification questions when the intent classifier
    has low confidence or when queries are ambiguous.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with clarification message
    """
    logger.info("clarification_node_activated")
    
    # Get the latest user message
    user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    latest_query = user_messages[-1].content if user_messages else ""
    
    # Get classification details
    intent = state.get("intent", "unknown")
    entities = state.get("entities", {})
    
    # Generate clarification questions based on the query and missing information
    clarification_questions = []
    
    # Check for missing project context
    if not entities.get("project_id") and not entities.get("project_name"):
        clarification_questions.append("Which project are you asking about?")
    
    # Check for ambiguous intent
    if intent == "unknown" or state.get("requires_clarify", False):
        clarification_questions.append("Are you looking for information about:")
        clarification_questions.append("- Documents (drawings, specifications, approvals)")
        clarification_questions.append("- Schedule (tasks, timelines, critical path)")
        clarification_questions.append("- Cross-module analysis (document-schedule relationships)")
    
    # Check for missing time context for schedule queries
    if intent == "SCHED_INTENT" and not any(key in entities for key in ["date_range", "start_date", "end_date"]):
        clarification_questions.append("What time period are you interested in? (e.g., this month, Q1 2025, specific dates)")
    
    # Check for missing document type for document queries
    if intent == "DOC_INTENT" and not entities.get("document_type"):
        clarification_questions.append("What type of documents are you looking for? (e.g., drawings, specifications, reports)")
    
    # Default clarification if no specific questions
    if not clarification_questions:
        clarification_questions = [
            "I need more information to help you effectively.",
            "Could you please provide more details about what you're looking for?"
        ]
    
    # Create clarification message
    clarification_text = "\n".join(clarification_questions)
    clarification_message = AIMessage(
        content=f"I need some clarification to better assist you:\n\n{clarification_text}\n\nPlease provide more details so I can give you the most relevant information."
    )
    
    logger.info(
        "clarification_generated",
        intent=intent,
        questions_count=len(clarification_questions),
        query_preview=latest_query[:100] if latest_query else ""
    )
    
    return {
        **state,
        "messages": state["messages"] + [clarification_message],
        "requires_clarify": False  # Reset the flag
    }


def fallback_node(state: IcmsState) -> IcmsState:
    """Fallback node for handling errors and unexpected situations.
    
    This node provides graceful degradation when the system encounters
    errors or cannot process a query through normal channels.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with fallback response
    """
    logger.info("fallback_node_activated")
    
    # Get the latest user message for context
    user_messages = [msg for msg in state["messages"] if isinstance(msg, HumanMessage)]
    latest_query = user_messages[-1].content if user_messages else ""
    
    # Determine fallback strategy based on available information
    intent = state.get("intent", "unknown")
    entities = state.get("entities", {})
    
    fallback_responses = []
    
    # Provide basic guidance based on detected intent
    if intent == "DOC_INTENT":
        fallback_responses.extend([
            "I understand you're looking for document information.",
            "Here are some things I can help you with:",
            "• Search for project documents by type or keyword",
            "• Find document approval status and versions",
            "• Analyze document relationships and dependencies"
        ])
    elif intent == "SCHED_INTENT":
        fallback_responses.extend([
            "I understand you're looking for schedule information.",
            "Here are some things I can help you with:",
            "• View project schedules and task status",
            "• Analyze critical path and potential delays",
            "• Check resource allocation and conflicts"
        ])
    elif intent == "CROSS_INTENT":
        fallback_responses.extend([
            "I understand you're looking for cross-module analysis.",
            "Here are some things I can help you with:",
            "• Analyze how document changes affect schedules",
            "• Map dependencies between documents and tasks",
            "• Assess project risks and compliance status"
        ])
    else:
        fallback_responses.extend([
            "I'm here to help you with construction project information.",
            "I can assist you with:",
            "• Document management (search, analysis, relationships)",
            "• Schedule management (tasks, critical path, resources)",
            "• Cross-module analysis (document-schedule integration)"
        ])
    
    # Add general guidance
    fallback_responses.extend([
        "",
        "To get started, try asking questions like:",
        "• 'Show me overdue tasks for Project Alpha'",
        "• 'Find structural drawings for foundation work'",
        "• 'What documents are blocking next week's concrete pour?'"
    ])
    
    # Create fallback message
    fallback_text = "\n".join(fallback_responses)
    fallback_message = AIMessage(
        content=f"I apologize, but I encountered an issue processing your request.\n\n{fallback_text}"
    )
    
    logger.info(
        "fallback_response_generated",
        intent=intent,
        query_preview=latest_query[:100] if latest_query else "",
        response_length=len(fallback_text)
    )
    
    return {
        **state,
        "messages": state["messages"] + [fallback_message],
        "requires_clarify": False  # Reset any error flags
    }


def error_recovery_node(state: IcmsState) -> IcmsState:
    """Error recovery node for handling system errors gracefully.
    
    This node attempts to recover from errors by providing helpful
    information and guidance to the user.
    
    Args:
        state: Current ICMS state
        
    Returns:
        Updated state with error recovery response
    """
    logger.info("error_recovery_node_activated")
    
    error_message = AIMessage(
        content="""I encountered a technical issue while processing your request. Here's what you can try:

1. **Rephrase your question** - Sometimes a different wording helps me understand better
2. **Be more specific** - Include project names, dates, or document types
3. **Try a simpler query** - Break complex requests into smaller parts

**Example queries that work well:**
• "Show me tasks starting this month"
• "Find approved drawings for Project Alpha"
• "What's the critical path for the current project?"

If the issue persists, please contact your system administrator."""
    )
    
    logger.info("error_recovery_response_generated")
    
    return {
        **state,
        "messages": state["messages"] + [error_message],
        "requires_clarify": False
    }
