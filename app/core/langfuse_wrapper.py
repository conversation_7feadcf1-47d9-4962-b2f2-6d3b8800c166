"""Langfuse wrapper that completely disables OpenTelemetry integration.

This module provides a safe way to use Langfuse without triggering
OpenTelemetry connection errors.
"""

import os
from typing import Optional, Any
from unittest.mock import Mock


class NoOpLangfuse:
    """No-operation Langfuse client that does nothing."""
    
    def __init__(self, *args, **kwargs):
        pass
    
    def __getattr__(self, name: str) -> Any:
        """Return a mock for any method call."""
        return Mock()
    
    def trace(self, *args, **kwargs):
        """Mock trace method."""
        return Mock()
    
    def generation(self, *args, **kwargs):
        """Mock generation method."""
        return Mock()
    
    def span(self, *args, **kwargs):
        """Mock span method."""
        return Mock()
    
    def flush(self):
        """Mock flush method."""
        pass
    
    def shutdown(self):
        """Mock shutdown method."""
        pass


def create_safe_langfuse(
    public_key: Optional[str] = None,
    secret_key: Optional[str] = None,
    host: str = "https://cloud.langfuse.com",
    **kwargs
) -> Optional[Any]:
    """Create a Langfuse client that won't cause telemetry errors.
    
    Args:
        public_key: Langfuse public key
        secret_key: Langfuse secret key
        host: Langfuse host URL
        **kwargs: Additional arguments
    
    Returns:
        Langfuse client or None if credentials are missing
    """
    # If no credentials provided, return None
    if not public_key or not secret_key:
        return None
    
    # Disable OpenTelemetry before importing Langfuse
    original_env = {}
    telemetry_vars = {
        "OTEL_SDK_DISABLED": "true",
        "OTEL_EXPORTER_OTLP_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_METRICS_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_LOGS_ENDPOINT": "",
        "OTEL_TRACES_EXPORTER": "none",
        "OTEL_METRICS_EXPORTER": "none",
        "OTEL_LOGS_EXPORTER": "none",
    }
    
    # Save original environment variables
    for key, value in telemetry_vars.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    try:
        # Import and create Langfuse client
        from langfuse import Langfuse
        
        # Create client with telemetry disabled
        client = Langfuse(
            public_key=public_key,
            secret_key=secret_key,
            host=host,
            **kwargs
        )
        
        return client
        
    except Exception as e:
        # If Langfuse fails to initialize, return a no-op client
        print(f"Warning: Langfuse initialization failed: {e}")
        return NoOpLangfuse()
    
    finally:
        # Restore original environment variables
        for key, original_value in original_env.items():
            if original_value is None:
                os.environ.pop(key, None)
            else:
                os.environ[key] = original_value
