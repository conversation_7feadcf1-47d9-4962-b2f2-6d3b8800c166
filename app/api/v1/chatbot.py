"""Chatbot API endpoints for handling chat interactions.

This module provides endpoints for chat interactions, including regular chat,
streaming chat, message history management, and chat history clearing.
"""

import json
from typing import List

from fastapi import (
    APIRouter,
    Depends,
    HTTPException,
    Request,
)
from fastapi.responses import StreamingResponse
from app.core.metrics import llm_stream_duration_seconds
from app.core.external_auth import get_current_user
from app.services.database import DatabaseService
from app.core.config import settings
from app.core.langgraph.graph import LangGraph<PERSON>gent
from app.core.limiter import limiter
from app.core.logging import logger
from app.models.session import Session
from app.schemas.chat import (
    ChatRequest,
    ChatResponse,
    Message,
    StreamResponse,
)

router = APIRouter()
agent = LangGraphAgent()
db_service = DatabaseService()



@router.post("/chat/{session_id}", response_model=ChatResponse)
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS["chat"][0])
async def chat(
    request: Request,
    session_id: str,
    chat_request: ChatRequest,
):
    """Process a chat request using LangGraph.

    Args:
        request: The FastAPI request object for rate limiting.
        session_id: The session ID from the URL path.
        chat_request: The chat request containing messages.

    Returns:
        ChatResponse: The processed chat response.

    Raises:
        HTTPException: If there's an error processing the request.
    """
    try:
        # Use session_id directly without database operations
        user_id = chat_request.user_id or "anonymous"
        
        logger.info(
            "chat_request_received",
            session_id=session_id,
            user_id=user_id,
            message_count=len(chat_request.messages),
            schedule_id=chat_request.schedule_id,
        )

        result = await agent.get_response(
            chat_request.messages, session_id, user_id=user_id, schedule_id=chat_request.schedule_id, auth_token=chat_request.auth_token
        )

        logger.info("chat_request_processed", session_id=session_id)

        return ChatResponse(messages=result)
    except Exception as e:
        logger.error("chat_request_failed", session_id=session_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/chat/{session_id}/stream")
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS["chat_stream"][0])
async def chat_stream(
    request: Request,
    session_id: str,
    chat_request: ChatRequest,
):
    """Process a chat request using LangGraph with streaming response.

    Args:
        request: The FastAPI request object for rate limiting.
        session_id: The session ID from the URL path.
        chat_request: The chat request containing messages.

    Returns:
        StreamingResponse: A streaming response of the chat completion.

    Raises:
        HTTPException: If there's an error processing the request.
    """
    try:
        # Use session_id directly without database operations
        user_id = chat_request.user_id or "anonymous"
        
        logger.info(
            "stream_chat_request_received",
            session_id=session_id,
            user_id=user_id,
            message_count=len(chat_request.messages),
            schedule_id=chat_request.schedule_id,
        )

        async def event_generator():
            """Generate streaming events.

            Yields:
                str: Server-sent events in JSON format.

            Raises:
                Exception: If there's an error during streaming.
            """
            try:
                full_response = ""
                with llm_stream_duration_seconds.labels(model=agent.llm.model_name).time():
                    async for chunk in agent.get_stream_response(
                        chat_request.messages, session_id, user_id=user_id, schedule_id=chat_request.schedule_id, auth_token=chat_request.auth_token
                     ):
                        full_response += chunk
                        response = StreamResponse(content=chunk, done=False)
                        yield f"data: {json.dumps(response.model_dump())}\n\n"

                # Send final message indicating completion
                final_response = StreamResponse(content="", done=True)
                yield f"data: {json.dumps(final_response.model_dump())}\n\n"

            except Exception as e:
                logger.error(
                    "stream_chat_request_failed",
                    session_id=session_id,
                    error=str(e),
                    exc_info=True,
                )
                error_response = StreamResponse(content=str(e), done=True)
                yield f"data: {json.dumps(error_response.model_dump())}\n\n"

        return StreamingResponse(event_generator(), media_type="text/event-stream")

    except Exception as e:
        logger.error(
            "stream_chat_request_failed",
            session_id=session_id,
            error=str(e),
            exc_info=True,
        )
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/messages/{session_id}", response_model=ChatResponse)
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS["messages"][0])
async def get_session_messages(
    request: Request,
    session_id: str,
    user_data: dict = Depends(get_current_user),
):
    """Get all messages for a session.

    Args:
        request: The FastAPI request object for rate limiting.
        session_id: The session ID from the URL path.
        user_data: User information from external auth.

    Returns:
        ChatResponse: All messages in the session.

    Raises:
        HTTPException: If there's an error retrieving the messages.
    """
    try:
        # Get session and verify ownership
        session = await db_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
            
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if session.external_user_id != external_user_id:
            raise HTTPException(status_code=403, detail="Cannot access other users' sessions")
        
        messages = await agent.get_chat_history(session.id)
        return ChatResponse(messages=messages)
    except Exception as e:
        logger.error("get_messages_failed", session_id=session_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/messages/{session_id}")
@limiter.limit(settings.RATE_LIMIT_ENDPOINTS["messages"][0])
async def clear_chat_history(
    request: Request,
    session_id: str,
    user_data: dict = Depends(get_current_user),
):
    """Clear all messages for a session.

    Args:
        request: The FastAPI request object for rate limiting.
        session_id: The session ID from the URL path.
        user_data: User information from external auth.

    Returns:
        dict: A message indicating the chat history was cleared.
    """
    try:
        # Get session and verify ownership
        session = await db_service.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="Session not found")
            
        external_user_id = str(user_data.get("id") or user_data.get("user_id"))
        if session.external_user_id != external_user_id:
            raise HTTPException(status_code=403, detail="Cannot access other users' sessions")
        
        await agent.clear_chat_history(session.id)
        return {"message": "Chat history cleared successfully"}
    except Exception as e:
        logger.error("clear_chat_history_failed", session_id=session_id, error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/debug/force-graph-recreation")
async def force_graph_recreation():
    """Debug endpoint to force recreation of the LangGraph workflow.

    This endpoint forces the recreation of the graph to pick up new workflow changes
    without requiring a server restart. Use this during development only.
    """
    try:
        # Use the existing agent instance to force recreation
        agent.force_graph_recreation()
        logger.info("debug_graph_recreation_forced")
        return {"message": "Graph recreation forced successfully. Next request will use new workflow."}
    except Exception as e:
        logger.error("debug_graph_recreation_failed", error=str(e), exc_info=True)
        raise HTTPException(status_code=500, detail=str(e))
