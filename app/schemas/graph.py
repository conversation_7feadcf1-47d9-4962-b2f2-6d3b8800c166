"""This file contains the graph schema for the application."""

import re
import uuid
from typing import Annotated, Dict, List, Optional, TypedDict

from langchain_core.messages import BaseMessage
from langgraph.graph.message import add_messages
from pydantic import (
    BaseModel,
    Field,
    field_validator,
)


class IcmsState(TypedDict):
    """State schema for ICMS Multi-Agent System as defined in blueprint."""

    messages: Annotated[List[BaseMessage], add_messages]  # Only last 10 messages for prompt efficiency
    full_history_id: str  # Redis key for complete conversation history
    intent: Optional[str]
    entities: dict  # extracted project, date, etc.
    gql_response: Optional[dict]
    requires_clarify: bool
    user_jwt: str  # for row-level authz
    user_permissions: dict  # cached user permissions for ACL filtering


class GraphState(BaseModel):
    """Legacy state definition for backward compatibility."""

    messages: Annotated[list, add_messages] = Field(
        default_factory=list, description="The messages in the conversation"
    )
    session_id: str = Field(..., description="The unique identifier for the conversation session")
    schedule_id: Optional[str] = Field(default=None, description="Optional schedule ID for context")
    auth_token: Optional[str] = Field(default=None, description="Optional GraphQL auth token for backend API access")

    @field_validator("session_id")
    @classmethod
    def validate_session_id(cls, v: str) -> str:
        """Validate that the session ID is a valid UUID or follows safe pattern.

        Args:
            v: The thread ID to validate

        Returns:
            str: The validated session ID

        Raises:
            ValueError: If the session ID is not valid
        """
        # Try to validate as UUID
        try:
            uuid.UUID(v)
            return v
        except ValueError:
            # If not a UUID, check for safe characters only
            if not re.match(r"^[a-zA-Z0-9_\-]+$", v):
                raise ValueError("Session ID must contain only alphanumeric characters, underscores, and hyphens")
            return v
