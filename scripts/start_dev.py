#!/usr/bin/env python3
"""Development startup script that ensures telemetry is properly disabled."""

import os
import sys
import subprocess
from pathlib import Path

def disable_telemetry():
    """Disable all telemetry exports before starting the application."""
    telemetry_vars = {
        "OTEL_SDK_DISABLED": "true",
        "OTEL_EXPORTER_OTLP_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_TRACES_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_METRICS_ENDPOINT": "",
        "OTEL_EXPORTER_OTLP_LOGS_ENDPOINT": "",
        "OTEL_TRACES_EXPORTER": "none",
        "OTEL_METRICS_EXPORTER": "none",
        "OTEL_LOGS_EXPORTER": "none",
        "LANGFUSE_PUBLIC_KEY": "",
        "LANGFUSE_SECRET_KEY": "",
    }
    
    for key, value in telemetry_vars.items():
        os.environ[key] = value
    
    print("🔧 Telemetry disabled for development")

def main():
    """Start the development server with telemetry disabled."""
    # Get project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # Disable telemetry
    disable_telemetry()
    
    # Start the application
    print("🚀 Starting ICMS Multi-Agent System...")
    
    try:
        # Use uvicorn to start the application
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--reload",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--log-level", "info"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 Shutting down gracefully...")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
